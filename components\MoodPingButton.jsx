import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Modal, ScrollView } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { timeUtils } from '../utils/timeUtils';

const MoodPingButton = ({ currentMood, onMoodUpdate, circleId }) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedMood, setSelectedMood] = useState(currentMood);

  const moodOptions = [
    { emoji: '😊', name: 'Happy', color: '#FFD700' },
    { emoji: '😎', name: 'Chill', color: '#00CED1' },
    { emoji: '🔥', name: 'Hype', color: '#FF4500' },
    { emoji: '😴', name: 'Sleepy', color: '#9370DB' },
    { emoji: '😢', name: 'Sad', color: '#4169E1' },
    { emoji: '😡', name: 'Angry', color: '#DC143C' },
    { emoji: '😰', name: 'Stressed', color: '#FF6347' },
    { emoji: '🤔', name: 'Thinking', color: '#20B2AA' },
    { emoji: '🎉', name: 'Excited', color: '#FF1493' },
    { emoji: '😑', name: 'Meh', color: '#808080' },
  ];

  const canUpdateMood = !currentMood || timeUtils.isNewDay(currentMood?.updated_at);

  const handleMoodSelect = async (mood) => {
    try {
      await onMoodUpdate(circleId, mood);
      setSelectedMood(mood);
      setModalVisible(false);
    } catch (error) {
      console.error('Failed to update mood:', error);
    }
  };

  const getCurrentMoodDisplay = () => {
    if (!currentMood) {
      return { emoji: '❓', name: 'Set Mood', color: '#666666' };
    }
    
    const moodData = moodOptions.find(m => m.emoji === currentMood.emoji);
    return moodData || { emoji: currentMood.emoji, name: 'Custom', color: '#666666' };
  };

  const moodDisplay = getCurrentMoodDisplay();

  return (
    <>
      <TouchableOpacity
        style={[styles.container, !canUpdateMood && styles.disabledContainer]}
        onPress={() => canUpdateMood && setModalVisible(true)}
        disabled={!canUpdateMood}
      >
        <LinearGradient
          colors={canUpdateMood ? [moodDisplay.color + '40', moodDisplay.color + '20'] : ['#333333', '#222222']}
          style={styles.gradient}
        >
          <Text style={styles.emoji}>{moodDisplay.emoji}</Text>
          <Text style={styles.moodName}>{moodDisplay.name}</Text>
          {!canUpdateMood && (
            <Text style={styles.resetText}>Resets tomorrow</Text>
          )}
        </LinearGradient>
      </TouchableOpacity>

      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>How are you feeling?</Text>
            
            <ScrollView style={styles.moodGrid} showsVerticalScrollIndicator={false}>
              <View style={styles.moodRow}>
                {moodOptions.map((mood, index) => (
                  <TouchableOpacity
                    key={mood.emoji}
                    style={[
                      styles.moodOption,
                      index % 2 === 0 ? styles.leftMood : styles.rightMood
                    ]}
                    onPress={() => handleMoodSelect(mood)}
                  >
                    <LinearGradient
                      colors={[mood.color + '40', mood.color + '20']}
                      style={styles.moodGradient}
                    >
                      <Text style={styles.moodEmoji}>{mood.emoji}</Text>
                      <Text style={styles.moodOptionName}>{mood.name}</Text>
                    </LinearGradient>
                  </TouchableOpacity>
                ))}
              </View>
            </ScrollView>

            <TouchableOpacity
              style={styles.cancelButton}
              onPress={() => setModalVisible(false)}
            >
              <Text style={styles.cancelText}>Cancel</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    overflow: 'hidden',
    marginHorizontal: 16,
    marginVertical: 8,
  },
  disabledContainer: {
    opacity: 0.6,
  },
  gradient: {
    padding: 16,
    alignItems: 'center',
    minHeight: 80,
    justifyContent: 'center',
  },
  emoji: {
    fontSize: 32,
    marginBottom: 4,
  },
  moodName: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  resetText: {
    color: '#AAAAAA',
    fontSize: 12,
    marginTop: 4,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#1A1A1A',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingTop: 24,
    paddingHorizontal: 16,
    paddingBottom: 40,
    maxHeight: '70%',
  },
  modalTitle: {
    color: '#FFFFFF',
    fontSize: 24,
    fontWeight: '700',
    textAlign: 'center',
    marginBottom: 24,
  },
  moodGrid: {
    flex: 1,
  },
  moodRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  moodOption: {
    width: '48%',
    marginBottom: 12,
    borderRadius: 12,
    overflow: 'hidden',
  },
  leftMood: {
    marginRight: '2%',
  },
  rightMood: {
    marginLeft: '2%',
  },
  moodGradient: {
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 80,
  },
  moodEmoji: {
    fontSize: 28,
    marginBottom: 8,
  },
  moodOptionName: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },
  cancelButton: {
    marginTop: 16,
    padding: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    alignItems: 'center',
  },
  cancelText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default MoodPingButton; 