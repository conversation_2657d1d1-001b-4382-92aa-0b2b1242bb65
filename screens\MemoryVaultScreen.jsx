import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  RefreshControl,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useCircleStore } from '../store/circleStore';
import MemoryVaultItem from '../components/MemoryVaultItem';

const MemoryVaultScreen = ({ navigation }) => {
  const [refreshing, setRefreshing] = useState(false);
  const [savedPosts, setSavedPosts] = useState([]);
  const [selectedFolder, setSelectedFolder] = useState('all');

  const { activeCircle, user } = useCircleStore();

  const folders = [
    { id: 'all', name: 'All Memories', emoji: '💾', count: savedPosts.length },
    { id: 'funny', name: 'Funny Moments', emoji: '😂', count: savedPosts.filter(p => p.folder === 'funny').length },
    { id: 'adventures', name: 'Adventures', emoji: '🌟', count: savedPosts.filter(p => p.folder === 'adventures').length },
    { id: 'special', name: 'Special Days', emoji: '🎉', count: savedPosts.filter(p => p.folder === 'special').length },
  ];

  useEffect(() => {
    loadSavedPosts();
  }, [activeCircle]);

  const loadSavedPosts = async () => {
    if (!activeCircle) return;

    try {
      // TODO: Implement actual Supabase query for saved posts
      // For now, use mock data
      const mockSavedPosts = [
        {
          id: '1',
          post_id: 'post_1',
          circle_id: activeCircle.id,
          saved_at: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
          votes_count: 8,
          total_members: 10,
          folder: 'funny',
          post: {
            id: 'post_1',
            type: 'image',
            content: null,
            media_url: 'https://picsum.photos/400/600',
            created_at: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
            users: {
              id: 'user_1',
              username: 'alex_party',
              avatar_url: null,
            },
          },
        },
        {
          id: '2',
          post_id: 'post_2',
          circle_id: activeCircle.id,
          saved_at: new Date(Date.now() - 259200000).toISOString(), // 3 days ago
          votes_count: 7,
          total_members: 10,
          folder: 'adventures',
          post: {
            id: 'post_2',
            type: 'text',
            content: 'Best pizza night ever! 🍕 Missing you all already',
            media_url: null,
            created_at: new Date(Date.now() - 345600000).toISOString(), // 4 days ago
            users: {
              id: 'user_2',
              username: 'pizza_lover',
              avatar_url: null,
            },
          },
        },
      ];

      setSavedPosts(mockSavedPosts);
    } catch (error) {
      console.error('Error loading saved posts:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadSavedPosts();
    setRefreshing(false);
  };

  const handleRemoveFromVault = async (savedPostId) => {
    Alert.alert(
      'Remove from Vault',
      'Are you sure you want to remove this memory from the vault?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: async () => {
            try {
              // TODO: Implement actual removal from Supabase
              setSavedPosts(prev => prev.filter(p => p.id !== savedPostId));
              Alert.alert('Removed', 'Memory removed from vault');
            } catch (error) {
              console.error('Error removing from vault:', error);
              Alert.alert('Error', 'Failed to remove memory');
            }
          },
        },
      ]
    );
  };

  const handleMoveToFolder = (savedPostId, folderId) => {
    Alert.alert(
      'Move Memory',
      `Move this memory to ${folders.find(f => f.id === folderId)?.name}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Move',
          onPress: () => {
            setSavedPosts(prev =>
              prev.map(p =>
                p.id === savedPostId ? { ...p, folder: folderId } : p
              )
            );
            Alert.alert('Moved', 'Memory moved successfully');
          },
        },
      ]
    );
  };

  const getFilteredPosts = () => {
    if (selectedFolder === 'all') {
      return savedPosts;
    }
    return savedPosts.filter(post => post.folder === selectedFolder);
  };

  const renderHeader = () => (
    <View style={styles.header}>
      <View style={styles.headerTop}>
        <Text style={styles.title}>Memory Vault</Text>
        {activeCircle && (
          <Text style={styles.circleIndicator}>
            {activeCircle.emoji} {activeCircle.name}
          </Text>
        )}
      </View>
      
      <Text style={styles.subtitle}>
        Saved memories from your circle that won't disappear
      </Text>
    </View>
  );

  const renderFolderSelector = () => (
    <View style={styles.folderSelector}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.folderList}
      >
        {folders.map((folder) => (
          <TouchableOpacity
            key={folder.id}
            style={[
              styles.folderButton,
              selectedFolder === folder.id && styles.activeFolderButton
            ]}
            onPress={() => setSelectedFolder(folder.id)}
          >
            <Text style={styles.folderEmoji}>{folder.emoji}</Text>
            <Text
              style={[
                styles.folderName,
                selectedFolder === folder.id && styles.activeFolderName
              ]}
            >
              {folder.name}
            </Text>
            <Text
              style={[
                styles.folderCount,
                selectedFolder === folder.id && styles.activeFolderCount
              ]}
            >
              {folder.count}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Text style={styles.emptyStateEmoji}>
        {selectedFolder === 'all' ? '💾' : folders.find(f => f.id === selectedFolder)?.emoji}
      </Text>
      <Text style={styles.emptyStateTitle}>
        {selectedFolder === 'all' ? 'No Memories Yet' : 'No Memories in This Folder'}
      </Text>
      <Text style={styles.emptyStateDescription}>
        {selectedFolder === 'all'
          ? 'When your circle votes to save posts, they\'ll appear here as permanent memories.'
          : 'Organize your saved memories by moving them to different folders.'}
      </Text>
      
      {selectedFolder === 'all' && (
        <TouchableOpacity
          style={styles.createPostButton}
          onPress={() => navigation.navigate('Create')}
        >
          <LinearGradient
            colors={['#7C4DFF', '#18FFFF']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={styles.gradientButton}
          >
            <Text style={styles.buttonText}>Create Your First Post</Text>
          </LinearGradient>
        </TouchableOpacity>
      )}
    </View>
  );

  const renderMemoryList = () => {
    const filteredPosts = getFilteredPosts();

    if (filteredPosts.length === 0) {
      return renderEmptyState();
    }

    return (
      <View style={styles.memoryList}>
        {filteredPosts.map((savedPost) => (
          <MemoryVaultItem
            key={savedPost.id}
            savedPost={savedPost}
            onRemove={() => handleRemoveFromVault(savedPost.id)}
            onMoveToFolder={(folderId) => handleMoveToFolder(savedPost.id, folderId)}
            folders={folders.filter(f => f.id !== 'all')}
          />
        ))}
      </View>
    );
  };

  if (!activeCircle) {
    return (
      <LinearGradient
        colors={['#0D0D0D', '#1A1A1A']}
        style={styles.container}
      >
        <SafeAreaView style={styles.safeArea}>
          <View style={styles.emptyState}>
            <Text style={styles.emptyStateEmoji}>🔒</Text>
            <Text style={styles.emptyStateTitle}>Select a Circle</Text>
            <Text style={styles.emptyStateDescription}>
              Choose a circle to view its memory vault
            </Text>
            <TouchableOpacity
              style={styles.createPostButton}
              onPress={() => navigation.navigate('Circles')}
            >
              <LinearGradient
                colors={['#7C4DFF', '#18FFFF']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.gradientButton}
              >
                <Text style={styles.buttonText}>View Circles</Text>
              </LinearGradient>
            </TouchableOpacity>
          </View>
        </SafeAreaView>
      </LinearGradient>
    );
  }

  return (
    <LinearGradient
      colors={['#0D0D0D', '#1A1A1A']}
      style={styles.container}
    >
      <SafeAreaView style={styles.safeArea}>
        <ScrollView
          style={styles.scrollView}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              tintColor="#7C4DFF"
              colors={['#7C4DFF']}
            />
          }
        >
          {renderHeader()}
          {renderFolderSelector()}
          {renderMemoryList()}
        </ScrollView>
      </SafeAreaView>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: 8,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  circleIndicator: {
    fontSize: 14,
    color: '#7C4DFF',
    fontWeight: '600',
  },
  subtitle: {
    fontSize: 16,
    color: '#AAAAAA',
    lineHeight: 22,
  },
  folderSelector: {
    marginVertical: 20,
  },
  folderList: {
    paddingHorizontal: 20,
    paddingRight: 40,
  },
  folderButton: {
    backgroundColor: '#2A2A2A',
    borderRadius: 16,
    padding: 16,
    marginRight: 12,
    alignItems: 'center',
    minWidth: 100,
  },
  activeFolderButton: {
    backgroundColor: '#7C4DFF',
  },
  folderEmoji: {
    fontSize: 24,
    marginBottom: 8,
  },
  folderName: {
    fontSize: 12,
    color: '#AAAAAA',
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 4,
  },
  activeFolderName: {
    color: '#FFFFFF',
  },
  folderCount: {
    fontSize: 10,
    color: '#666666',
    fontWeight: '500',
  },
  activeFolderCount: {
    color: '#DDDDDD',
  },
  memoryList: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 40,
    paddingVertical: 60,
  },
  emptyStateEmoji: {
    fontSize: 64,
    marginBottom: 16,
  },
  emptyStateTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyStateDescription: {
    fontSize: 16,
    color: '#AAAAAA',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
  },
  createPostButton: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  gradientButton: {
    paddingVertical: 16,
    paddingHorizontal: 32,
    alignItems: 'center',
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default MemoryVaultScreen; 