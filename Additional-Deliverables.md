# Additional Deliverables for Cursor

## 1. **Wireframes or Screens**

Even **low-fidelity wireframes** or sketches will save your devs tons of guesswork.

Suggested Screens:

* **Onboarding**:

  * Sign Up / Login
  * Create Circle / Join Circle
* **Circle Home**:

  * Current mood display
  * Content feed (disappearing posts)
  * Post creation (camera, text, voice)
* **Reaction UI**:

  * Emoji picker
  * Record voice reply
  * Record quick video reply
* **Memory Vault**
* **Settings**:

  * Privacy options
  * Notification controls

**Tools:** Figma, Pen & Paper scans, Excalidraw, or screenshots with annotations.

---

## 2. **API Requirements & Endpoints**

If using Supabase or Firebase, define:

* **Auth**:

  * Signup/login (email, phone, or social?)
* **Circle Management**:

  * Create / Join / Leave Circle
  * Invite friends
* **Content Feed**:

  * Upload content (photo, video, text, voice)
  * Fetch Circle feed
  * Delete expired content automatically
* **Reactions**:

  * Post reaction (emoji, voice, video)
* **Memory Vault**:

  * Save to Vault (vote-based logic)
* **Mood Ping**:

  * Update daily mood

---

## 3. **Component List**

Give the team a breakdown of reusable components:

* **CircleCard**
* **PostCard**
* **ReactionBar**
* **MoodPingButton**
* **MemoryVaultItem**
* **InviteFriendModal**

This helps structure the code cleanly from the start.

---

## 4. **Animation & Interaction Notes**

If you have specific animations in mind:

* **Content melt-away animation** when posts disappear
* **Haptic feedback** on reactions
* **Smooth swipe transitions** between Circles

If not, let them know to use **Framer Motion (React Native Reanimated)** or **Expo Easing** for smooth interactions.

---

## 5. **Copy & Microcopy**

Even placeholder text will help:

* Welcome message
* Mood options (e.g., Chill, Hype, Stressed)
* Reaction prompts (e.g., “Say something back!”)
* Memory Vault empty state text

---

## 6. **Tech Stack Confirmation**

Clarify:

* **Expo version**
* **Supabase vs Firebase?**
* Media storage provider (Supabase Storage, S3, etc.)
* State management (Zustand, Redux, Context API?)
* Navigation (React Navigation stack/screens plan)

---

## 7. **Security & Privacy Requirements**

Give clear rules:

* No public data exposure
* Screenshot prevention (if possible)
* Encryption at rest & in transit
* Content deletion policy

---

## 8. **MVP vs Phase 2 Features**

Mark clearly what's **MVP** and what's **Phase 2**, so the devs can prioritize.

For example:

| **Feature**                     | **MVP?**    |
| ------------------------------- | ----------- |
| Circle Creation / Join          | ✅           |
| Disappearing Content            | ✅           |
| Emoji / Voice / Video Reactions | ✅           |
| Memory Vault                    | ✅           |
| Customizable Avatars            | ❌ (Phase 2) |
| AR Filters                      | ❌ (Phase 2) |

---

## 9. **Testing & QA Plan**

* Should devs set up **Jest / Detox / Expo Go testing?**
* Any manual test scripts you want them to follow?

---

## 10. **Branding & Assets**

* Color palette
* Logo (even if placeholder)
* Fonts or icon packs (e.g., Phosphor, Lucide)
* App icon & splash screen (for Expo)

---