# Secret Circle

A private social app built with Expo React Native that lets teens create invite-only friend groups to share disappearing content, react in real-time, and keep their closest connections fun, low-pressure, and authentic.

## 🚀 Quick Start

### Prerequisites

- Node.js (v14 or later)
- Expo CLI (`npm install -g expo-cli`)
- iOS Simulator or Android Emulator (or Expo Go app on your device)

### Installation

1. Clone the repository
2. Navigate to the project directory
```bash
cd SecretCircle
```

3. Install dependencies
```bash
npm install
```

4. Set up environment variables
```bash
# Create a .env file with your Supabase credentials
SUPABASE_URL=your_supabase_url_here
SUPABASE_ANON_KEY=your_supabase_anon_key_here
```

5. Start the development server
```bash
npx expo start
```

## 📱 Features

### Core Features (MVP)
- ✅ **Circle Creation & Management** - Create private groups up to 10 friends
- ✅ **Disappearing Content Feed** - Posts disappear after 24 hours unless reacted to
- ✅ **Multi-format Reactions** - Emoji, voice (5s), and video (5s) reactions
- ✅ **Memory Vault** - Vote to save posts permanently with majority vote
- ✅ **Mood Ping** - Daily mood check-ins that reset each day
- ✅ **Privacy & Safety** - No public feeds, invite-only, encrypted content

### Content Types Supported
- 📸 Photos
- 🎥 Videos (up to 15 seconds)
- 🎵 Voice notes (up to 10 seconds) 
- 💬 Text posts

## 🏗 Project Structure

```
/SecretCircle
├── /assets              # Images, icons, fonts
│   ├── /icons
│   ├── /images
│   └── /fonts
├── /components          # Reusable UI components
│   ├── CircleCard.jsx
│   ├── PostCard.jsx
│   ├── ReactionBar.jsx
│   ├── MoodPingButton.jsx
│   └── MemoryVaultItem.jsx
├── /screens            # Screen components
│   ├── OnboardingScreen.jsx
│   ├── CircleHomeScreen.jsx
│   ├── PostCreationScreen.jsx
│   ├── MemoryVaultScreen.jsx
│   └── SettingsScreen.jsx
├── /services           # External service integrations
│   ├── supabaseClient.js
│   ├── mediaUpload.js
│   └── notificationService.js
├── /store              # State management (Zustand)
│   └── circleStore.js
├── /utils              # Utility functions
│   ├── timeUtils.js
│   └── permissions.js
└── App.js              # Main app component
```

## 🎨 Design System

### Colors
- **Primary Gradient**: `#7C4DFF` → `#18FFFF`
- **Background**: `#0D0D0D`
- **Cards**: `#1A1A1A`
- **Accent**: `#FF4081`
- **Text**: `#FFFFFF`
- **Subtext**: `#AAAAAA`

### Typography
- **Primary**: Inter SemiBold
- **Secondary**: Inter Regular

## 🔧 Tech Stack

| Technology | Tool |
|------------|------|
| Framework | Expo (React Native) |
| Backend | Supabase |
| State Management | Zustand |
| Navigation | React Navigation |
| Animations | React Native Reanimated |
| Media Handling | Expo AV, Camera, ImagePicker |
| Notifications | Expo Notifications |

## 📋 Development Status

### ✅ Completed
- [x] Project setup and folder structure
- [x] Supabase client configuration
- [x] Core components (CircleCard, PostCard, ReactionBar, etc.)
- [x] State management with Zustand
- [x] Media upload services
- [x] Time utilities for post expiration
- [x] Permission handling utilities

### 🚧 In Progress
- [ ] Screen implementations
- [ ] Navigation setup
- [ ] Authentication flow

### 📅 TODO
- [ ] Circle management functionality
- [ ] Content feed with real-time updates
- [ ] Reaction system implementation
- [ ] Memory vault with voting
- [ ] Push notifications
- [ ] Database schema setup

## 🔐 Privacy & Security

- **End-to-end encrypted** content storage
- **No public feeds** or discoverable profiles
- **Invite-only** system with no search functionality
- **Content auto-deletion** after 24 hours (unless reacted to)
- **Screenshot detection** (where possible)

## 📊 Key Metrics

- Daily active users per Circle
- Average reactions per post
- Circle retention after 7, 14, 30 days
- Virality coefficient (invites sent per user)

## 🚀 Deployment

The app will be deployed to:
- **iOS**: App Store (TestFlight for beta)
- **Android**: Google Play Store (Internal testing for beta)

## 📄 License

This project is proprietary software. All rights reserved.

## 🤝 Contributing

This is a private project. Please contact the development team for contribution guidelines. 