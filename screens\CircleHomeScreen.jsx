import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  RefreshControl,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useCircleStore } from '../store/circleStore';
import { timeUtils } from '../utils/timeUtils';

// Import components
import CircleCard from '../components/CircleCard';
import PostCard from '../components/PostCard';
import MoodPingButton from '../components/MoodPingButton';

const CircleHomeScreen = ({ navigation }) => {
  const [refreshing, setRefreshing] = useState(false);
  const [currentMood, setCurrentMood] = useState(null);

  const {
    circles,
    activeCircle,
    posts,
    user,
    isLoading,
    setActiveCircle,
    fetchCircles,
    fetchPosts,
  } = useCircleStore();

  useEffect(() => {
    loadInitialData();
  }, []);

  useEffect(() => {
    if (activeCircle) {
      loadPosts();
      loadCircleMood();
    }
  }, [activeCircle]);

  const loadInitialData = async () => {
    try {
      await fetchCircles();
    } catch (error) {
      console.error('Error loading circles:', error);
    }
  };

  const loadPosts = async () => {
    if (!activeCircle) return;
    
    try {
      await fetchPosts(activeCircle.id);
    } catch (error) {
      console.error('Error loading posts:', error);
    }
  };

  const loadCircleMood = async () => {
    // TODO: Implement mood loading from Supabase
    // For now, set a mock mood
    setCurrentMood({
      emoji: '😊',
      name: 'Happy',
      updated_at: new Date().toISOString(),
    });
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadInitialData();
    if (activeCircle) {
      await loadPosts();
      await loadCircleMood();
    }
    setRefreshing(false);
  };

  const handleCircleSelect = (circle) => {
    setActiveCircle(circle);
  };

  const handleMoodUpdate = async (circleId, mood) => {
    try {
      // TODO: Implement mood update to Supabase
      setCurrentMood({
        emoji: mood.emoji,
        name: mood.name,
        updated_at: new Date().toISOString(),
      });
      Alert.alert('Mood Updated', `Your mood is now ${mood.emoji} ${mood.name}`);
    } catch (error) {
      console.error('Error updating mood:', error);
      Alert.alert('Error', 'Failed to update mood');
    }
  };

  const handleInviteFriends = () => {
    if (!activeCircle) return;
    
    // Generate a mock invite code
    const inviteCode = `${activeCircle.name.substring(0, 3).toUpperCase()}${Math.random().toString(36).substring(2, 8).toUpperCase()}`;
    
    Alert.alert(
      'Invite Friends',
      `Share this code with your friends:\n\n${inviteCode}`,
      [
        { text: 'Copy Code', onPress: () => {
          // TODO: Implement clipboard copy
          Alert.alert('Copied!', 'Invite code copied to clipboard');
        }},
        { text: 'OK' }
      ]
    );
  };

  const handleCreatePost = () => {
    navigation.navigate('Create');
  };

  const renderHeader = () => (
    <View style={styles.header}>
      <View style={styles.headerTop}>
        <Text style={styles.greeting}>
          Hey {user?.username || 'Friend'}! 👋
        </Text>
        <TouchableOpacity 
          style={styles.inviteButton}
          onPress={handleInviteFriends}
        >
          <Text style={styles.inviteButtonText}>📎</Text>
        </TouchableOpacity>
      </View>
      
      {activeCircle && (
        <View style={styles.circleInfo}>
          <Text style={styles.circleName}>
            {activeCircle.emoji} {activeCircle.name}
          </Text>
          <Text style={styles.circleMembers}>
            {activeCircle.memberCount || 1} members
          </Text>
        </View>
      )}
    </View>
  );

  const renderCircleSelector = () => (
    <View style={styles.circleSelector}>
      <Text style={styles.sectionTitle}>Your Circles</Text>
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.circleList}
      >
        {circles.map((circle) => (
          <CircleCard
            key={circle.id}
            circle={circle}
            onPress={handleCircleSelect}
            isActive={activeCircle?.id === circle.id}
          />
        ))}
        
        {circles.length < 5 && (
          <TouchableOpacity 
            style={styles.addCircleCard}
            onPress={() => setShowCreateModal(true)}
          >
            <Text style={styles.addCircleEmoji}>➕</Text>
            <Text style={styles.addCircleText}>Add Circle</Text>
          </TouchableOpacity>
        )}
      </ScrollView>
    </View>
  );

  const renderMoodSection = () => {
    if (!activeCircle) return null;

    return (
      <View style={styles.moodSection}>
        <Text style={styles.sectionTitle}>Circle Vibe</Text>
        <MoodPingButton
          currentMood={currentMood}
          onMoodUpdate={handleMoodUpdate}
          circleId={activeCircle.id}
        />
      </View>
    );
  };

  const renderFeed = () => {
    if (!activeCircle) {
      return (
        <View style={styles.emptyState}>
          <Text style={styles.emptyStateEmoji}>🔒</Text>
          <Text style={styles.emptyStateTitle}>Select a Circle</Text>
          <Text style={styles.emptyStateDescription}>
            Choose a circle to see posts and start sharing with your friends
          </Text>
        </View>
      );
    }

    if (posts.length === 0) {
      return (
        <View style={styles.emptyState}>
          <Text style={styles.emptyStateEmoji}>✨</Text>
          <Text style={styles.emptyStateTitle}>No Posts Yet</Text>
          <Text style={styles.emptyStateDescription}>
            Be the first to share something in {activeCircle.name}!
          </Text>
          <TouchableOpacity 
            style={styles.createFirstPostButton}
            onPress={handleCreatePost}
          >
            <LinearGradient
              colors={['#7C4DFF', '#18FFFF']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={styles.gradientButton}
            >
              <Text style={styles.buttonText}>Create First Post</Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>
      );
    }

    return (
      <View style={styles.feedSection}>
        <View style={styles.feedHeader}>
          <Text style={styles.sectionTitle}>Feed</Text>
          <TouchableOpacity 
            style={styles.createPostButton}
            onPress={handleCreatePost}
          >
            <Text style={styles.createPostButtonText}>➕ Post</Text>
          </TouchableOpacity>
        </View>
        
        {posts.map((post) => (
          <PostCard
            key={post.id}
            post={post}
            onReaction={(reactionType, content) => {
              // TODO: Implement reaction handling
              console.log('Reaction:', reactionType, content);
            }}
            onSaveToVault={() => {
              // TODO: Implement save to vault
              console.log('Save to vault:', post.id);
            }}
          />
        ))}
      </View>
    );
  };

  const renderQuickActions = () => {
    if (!activeCircle) return null;

    return (
      <View style={styles.quickActions}>
        <TouchableOpacity 
          style={styles.quickActionButton}
          onPress={() => navigation.navigate('Vault')}
        >
          <Text style={styles.quickActionEmoji}>💾</Text>
          <Text style={styles.quickActionText}>Vault</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.quickActionButton}
          onPress={handleCreatePost}
        >
          <Text style={styles.quickActionEmoji}>📸</Text>
          <Text style={styles.quickActionText}>Camera</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.quickActionButton}
          onPress={handleInviteFriends}
        >
          <Text style={styles.quickActionEmoji}>👥</Text>
          <Text style={styles.quickActionText}>Invite</Text>
        </TouchableOpacity>
      </View>
    );
  };

  if (circles.length === 0 && !isLoading) {
    return (
      <LinearGradient
        colors={['#0D0D0D', '#1A1A1A']}
        style={styles.container}
      >
        <SafeAreaView style={styles.safeArea}>
          <View style={styles.emptyState}>
            <Text style={styles.emptyStateEmoji}>🎯</Text>
            <Text style={styles.emptyStateTitle}>No Circles Yet</Text>
            <Text style={styles.emptyStateDescription}>
              Create your first circle or join one with an invite code
            </Text>
            <TouchableOpacity 
              style={styles.createFirstPostButton}
              onPress={() => setShowCreateModal(true)}
            >
              <LinearGradient
                colors={['#7C4DFF', '#18FFFF']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.gradientButton}
              >
                <Text style={styles.buttonText}>Get Started</Text>
              </LinearGradient>
            </TouchableOpacity>
          </View>
        </SafeAreaView>
      </LinearGradient>
    );
  }

  return (
    <LinearGradient
      colors={['#0D0D0D', '#1A1A1A']}
      style={styles.container}
    >
      <SafeAreaView style={styles.safeArea}>
        <ScrollView 
          style={styles.scrollView}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              tintColor="#7C4DFF"
              colors={['#7C4DFF']}
            />
          }
        >
          {renderHeader()}
          {renderCircleSelector()}
          {renderMoodSection()}
          {renderQuickActions()}
          {renderFeed()}
        </ScrollView>
      </SafeAreaView>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: 8,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  greeting: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  inviteButton: {
    backgroundColor: '#2A2A2A',
    borderRadius: 20,
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  inviteButtonText: {
    fontSize: 18,
  },
  circleInfo: {
    alignItems: 'center',
  },
  circleName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  circleMembers: {
    fontSize: 14,
    color: '#AAAAAA',
  },
  circleSelector: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 12,
    paddingHorizontal: 20,
  },
  circleList: {
    paddingHorizontal: 20,
    paddingRight: 40,
  },
  addCircleCard: {
    backgroundColor: '#2A2A2A',
    borderRadius: 12,
    width: 120,
    height: 80,
    marginRight: 12,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: '#333333',
    borderStyle: 'dashed',
  },
  addCircleEmoji: {
    fontSize: 24,
    marginBottom: 4,
  },
  addCircleText: {
    fontSize: 12,
    color: '#AAAAAA',
    fontWeight: '600',
  },
  moodSection: {
    marginBottom: 20,
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  quickActionButton: {
    backgroundColor: '#2A2A2A',
    borderRadius: 16,
    padding: 16,
    alignItems: 'center',
    minWidth: 80,
  },
  quickActionEmoji: {
    fontSize: 24,
    marginBottom: 4,
  },
  quickActionText: {
    fontSize: 12,
    color: '#FFFFFF',
    fontWeight: '600',
  },
  feedSection: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  feedHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  createPostButton: {
    backgroundColor: '#7C4DFF',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  createPostButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: 'bold',
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 40,
    paddingVertical: 60,
  },
  emptyStateEmoji: {
    fontSize: 64,
    marginBottom: 16,
  },
  emptyStateTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyStateDescription: {
    fontSize: 16,
    color: '#AAAAAA',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
  },
  createFirstPostButton: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  gradientButton: {
    paddingVertical: 16,
    paddingHorizontal: 32,
    alignItems: 'center',
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default CircleHomeScreen; 