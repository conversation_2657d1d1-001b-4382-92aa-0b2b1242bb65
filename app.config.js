export default {
  expo: {
    name: "Secret Circle",
    slug: "secret-circle",
    version: "1.0.0",
    orientation: "portrait",
    icon: "./assets/icon.png",
    userInterfaceStyle: "dark",
    splash: {
      image: "./assets/splash-icon.png",
      resizeMode: "contain",
      backgroundColor: "#0D0D0D"
    },
    assetBundlePatterns: [
      "**/*"
    ],
    ios: {
      supportsTablet: true,
      bundleIdentifier: "com.secretcircle.app"
    },
    android: {
      adaptiveIcon: {
        foregroundImage: "./assets/adaptive-icon.png",
        backgroundColor: "#0D0D0D"
      },
      package: "com.secretcircle.app"
    },
    web: {
      favicon: "./assets/favicon.png"
    },
    extra: {
      supabaseUrl: "https://eohbewzbuqicwbbqassv.supabase.co",
      supabaseAnonKey: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVvaGJld3pidXFpY3diYnFhc3N2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI3OTE3NzcsImV4cCI6MjA2ODM2Nzc3N30.mrnt30zUUBLLSPZMNJRq9LBcpNGV-N0WOQZ6MXG-sJ8"
    }
  }
}; 