import React, { useState, useEffect } from 'react';
import { View, Text, Image, TouchableOpacity, StyleSheet, Dimensions } from 'react-native';
import { Video } from 'expo-av';
import { Audio } from 'expo-av';
import { LinearGradient } from 'expo-linear-gradient';
import { timeUtils } from '../utils/timeUtils';
import ReactionBar from './ReactionBar';

const { width: screenWidth } = Dimensions.get('window');

const PostCard = ({ post, onReact, onSaveToVault }) => {
  const [timeRemaining, setTimeRemaining] = useState(null);
  const [sound, setSound] = useState(null);

  useEffect(() => {
    // Update time remaining every minute
    const updateTimer = () => {
      const remaining = timeUtils.getTimeUntilExpiry(post);
      setTimeRemaining(remaining);
    };

    updateTimer();
    const interval = setInterval(updateTimer, 60000); // Update every minute

    return () => {
      clearInterval(interval);
      if (sound) {
        sound.unloadAsync();
      }
    };
  }, [post, sound]);

  const playAudio = async () => {
    try {
      if (sound) {
        await sound.unloadAsync();
      }
      
      const { sound: newSound } = await Audio.Sound.createAsync(
        { uri: post.content_url },
        { shouldPlay: true }
      );
      
      setSound(newSound);
    } catch (error) {
      console.error('Error playing audio:', error);
    }
  };

  const renderContent = () => {
    switch (post.type) {
      case 'image':
        return (
          <Image 
            source={{ uri: post.content_url }} 
            style={styles.media}
            resizeMode="cover"
          />
        );
      
      case 'video':
        return (
          <Video
            source={{ uri: post.content_url }}
            style={styles.media}
            useNativeControls
            resizeMode="cover"
            isLooping={false}
          />
        );
      
      case 'audio':
        return (
          <TouchableOpacity onPress={playAudio} style={styles.audioContainer}>
            <LinearGradient
              colors={['#7C4DFF', '#18FFFF']}
              style={styles.audioGradient}
            >
              <Text style={styles.audioIcon}>🎵</Text>
              <Text style={styles.audioText}>Voice Note</Text>
            </LinearGradient>
          </TouchableOpacity>
        );
      
      case 'text':
      default:
        return (
          <Text style={styles.textContent}>
            {post.content}
          </Text>
        );
    }
  };

  const isExpired = timeUtils.isPostExpired(post);

  if (isExpired) {
    return null; // Don't render expired posts
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.userInfo}>
          <View style={styles.avatar}>
            <Text style={styles.avatarText}>
              {post.users?.username?.charAt(0).toUpperCase() || 'U'}
            </Text>
          </View>
          <View>
            <Text style={styles.username}>
              {post.users?.username || 'Unknown User'}
            </Text>
            <Text style={styles.timestamp}>
              {timeUtils.getRelativeTime(post.created_at)}
            </Text>
          </View>
        </View>
        
        <View style={styles.timerContainer}>
          <Text style={styles.timerText}>
            {timeUtils.formatTimeRemaining(timeRemaining)}
          </Text>
        </View>
      </View>

      {/* Content */}
      <View style={styles.content}>
        {renderContent()}
      </View>

      {/* Reaction Bar */}
      <ReactionBar 
        post={post}
        onReact={onReact}
        onSaveToVault={onSaveToVault}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#1A1A1A',
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 16,
    overflow: 'hidden',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    paddingBottom: 8,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#7C4DFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  avatarText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  username: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  timestamp: {
    color: '#AAAAAA',
    fontSize: 12,
  },
  timerContainer: {
    backgroundColor: 'rgba(255, 64, 129, 0.2)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  timerText: {
    color: '#FF4081',
    fontSize: 10,
    fontWeight: '600',
  },
  content: {
    paddingHorizontal: 16,
    paddingBottom: 8,
  },
  media: {
    width: '100%',
    height: 200,
    borderRadius: 12,
    backgroundColor: '#2A2A2A',
  },
  audioContainer: {
    height: 80,
    borderRadius: 12,
    overflow: 'hidden',
  },
  audioGradient: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  audioIcon: {
    fontSize: 24,
    marginRight: 8,
  },
  audioText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  textContent: {
    color: '#FFFFFF',
    fontSize: 16,
    lineHeight: 24,
    paddingVertical: 8,
  },
});

export default PostCard; 