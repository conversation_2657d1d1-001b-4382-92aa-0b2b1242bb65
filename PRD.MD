# Secret Circle – Product Requirements Document (PRD)

## Product Name

**Secret Circle**

## Platform

**Mobile App – Built with Expo (React Native)**
Targeting iOS and Android with a shared codebase for rapid development and smooth cross-platform deployment.

## Purpose

Secret Circle is a private social app that lets teens create invite-only friend groups to share disappearing content, react in real-time, and keep their closest connections fun, low-pressure, and authentic.

Unlike public social platforms, Secret Circle focuses on **real friends, not followers**.

## Key Objectives

* Create a **safe, private space** for small friend groups to hang out digitally
* Encourage **authentic sharing** without public likes or followers
* Make communication more playful and interactive with **voice, emoji, and video reactions**
* Deliver an exclusive, invite-only experience that drives organic growth through FOMO

---

# Core Features

## 1. Circle Creation & Management

### Description

Users create their own **Circle**, inviting up to 10 friends to join. Each Circle is private, closed, and non-discoverable.

### Functionality

* **Create Circle**:

  * Name the Circle
  * Select cover emoji or theme
  * Invite friends via phone contacts, QR code, or link

* **Join Circle**:

  * Accept invite link or QR scan
  * One user cannot be in more than **5 active Circles** at once (to prevent spam groups)

* **Roles**:

  * Creator can moderate invites and remove members
  * All members can post, react, and vote on saved memories

---

## 2. Disappearing Content Feed

### Description

Each Circle has a shared feed where members post short-lived content. Posts disappear after **24 hours unless someone reacts**.

### Content Types

* Photo
* Video (up to 15 seconds)
* Text update
* Voice note (up to 10 seconds)

### Special Rule

If no one reacts within 24 hours, the post is auto-deleted. If someone reacts, it extends for another 24 hours.

---

## 3. Reactions & Responses

### Description

Make the group dynamic fun and interactive, similar to a digital hangout.

### Types of Reactions

* Emoji reactions
* Voice replies (up to 5 seconds)
* Video replies (up to 5 seconds)
* Text comments (optional toggle per Circle)

### Interaction Loop

Reacting keeps content alive. No reactions = content disappears.

---

## 4. Memory Vault

### Description

Groups can vote to **save content** into a private Memory Vault for the Circle.

### Functionality

* Requires **majority vote** to save a post
* Saved posts stay in the Vault indefinitely
* Vault is only visible to Circle members
* Option to create themed folders inside the Vault (e.g., "Funniest Moments," "Summer Break")

---

## 5. Mood Ping

### Description

Quick check-ins using emojis, colors, or short messages to set the group vibe.

### Functionality

* Choose from preset moods or create custom ones
* Shows on the Circle’s main screen
* Moods reset daily to encourage fresh check-ins

---

## 6. Privacy & Safety

### Description

Secret Circle is designed for **maximum safety and control**.

### Features

* No discoverable profiles
* No search function for users or Circles
* No screenshots (optional screenshot detection with alert sent to the Circle)
* All content encrypted during transfer and storage
* No ads, no public feeds

---

# Technical Requirements

## Stack

* **Frontend**: Expo (React Native), React Navigation, Gesture Handler
* **Backend**: Supabase (Authentication, Database, Storage), or Firebase alternative
* **Media**: Cloud storage for images, videos, and voice notes (Supabase Storage or AWS S3)
* **Notifications**: Push notifications via Expo’s Notification API
* **Real-time**: WebSockets or Supabase Realtime for live reactions and updates

---

# UX & UI Style Guide

## Visual Style

* Soft neon gradients for Circle themes
* Minimalist, text-light UI
* Rounded corners, smooth animations
* Customizable color palettes per Circle

## Interaction Style

* Swipe gestures for posting, reacting, and navigating between Circles
* Haptic feedback on reactions
* Fun, playful microinteractions (e.g., content "melts away" when it expires)

---

# Monetization (Optional)

* **Premium Themes**: Purchase exclusive Circle color themes and animations
* **Reaction Packs**: Buy special emoji or voice effect packs
* **Memory Boost**: Option to expand Memory Vault capacity

---

# Launch Plan

## MVP Scope

* Circle creation and invites
* Disappearing content feed
* Emoji, voice, and video reactions
* Memory Vault basic version
* Mood Ping
* Push notifications

## Post-MVP Ideas

* AR filters for video posts
* Circle games or mini challenges
* Custom avatars

---

# Success Metrics

* Daily active users per Circle
* Average reactions per post
* Circle retention after 7, 14, 30 days
* Virality coefficient (invites sent per user)

---

If you'd like, I can create the wireframes or developer task board for Secret Circle next. Want me to set that up?
