import * as MediaLibrary from 'expo-media-library';
import * as Camera from 'expo-camera';
import { Audio } from 'expo-av';
import { Alert, Linking } from 'react-native';

export const permissions = {
  // Request camera permissions
  requestCameraPermission: async () => {
    try {
      const { status } = await Camera.requestCameraPermissionsAsync();
      
      if (status !== 'granted') {
        Alert.alert(
          'Camera Permission Required',
          'Secret Circle needs camera access to take photos and videos for your Circle.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Open Settings', onPress: () => Linking.openSettings() }
          ]
        );
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('Error requesting camera permission:', error);
      return false;
    }
  },

  // Request media library permissions
  requestMediaLibraryPermission: async () => {
    try {
      const { status } = await MediaLibrary.requestPermissionsAsync();
      
      if (status !== 'granted') {
        Alert.alert(
          'Photo Library Permission Required',
          'Secret Circle needs access to your photo library to select images and videos.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Open Settings', onPress: () => Linking.openSettings() }
          ]
        );
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('Error requesting media library permission:', error);
      return false;
    }
  },

  // Request audio recording permissions
  requestAudioPermission: async () => {
    try {
      const { status } = await Audio.requestPermissionsAsync();
      
      if (status !== 'granted') {
        Alert.alert(
          'Microphone Permission Required',
          'Secret Circle needs microphone access to record voice messages and reactions.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Open Settings', onPress: () => Linking.openSettings() }
          ]
        );
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('Error requesting audio permission:', error);
      return false;
    }
  },

  // Check if camera permission is granted
  checkCameraPermission: async () => {
    try {
      const { status } = await Camera.getCameraPermissionsAsync();
      return status === 'granted';
    } catch (error) {
      console.error('Error checking camera permission:', error);
      return false;
    }
  },

  // Check if media library permission is granted
  checkMediaLibraryPermission: async () => {
    try {
      const { status } = await MediaLibrary.getPermissionsAsync();
      return status === 'granted';
    } catch (error) {
      console.error('Error checking media library permission:', error);
      return false;
    }
  },

  // Check if audio permission is granted
  checkAudioPermission: async () => {
    try {
      const { status } = await Audio.getPermissionsAsync();
      return status === 'granted';
    } catch (error) {
      console.error('Error checking audio permission:', error);
      return false;
    }
  },

  // Request all necessary permissions at once
  requestAllPermissions: async () => {
    const permissions = await Promise.all([
      permissions.requestCameraPermission(),
      permissions.requestMediaLibraryPermission(),
      permissions.requestAudioPermission(),
    ]);

    return {
      camera: permissions[0],
      mediaLibrary: permissions[1],
      audio: permissions[2],
      allGranted: permissions.every(p => p === true)
    };
  },

  // Check all permissions status
  checkAllPermissions: async () => {
    const permissions = await Promise.all([
      permissions.checkCameraPermission(),
      permissions.checkMediaLibraryPermission(),
      permissions.checkAudioPermission(),
    ]);

    return {
      camera: permissions[0],
      mediaLibrary: permissions[1],
      audio: permissions[2],
      allGranted: permissions.every(p => p === true)
    };
  },
}; 