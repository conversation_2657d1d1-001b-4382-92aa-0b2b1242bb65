// Time utility functions for Secret Circle app

export const timeUtils = {
  // Check if a post has expired (24 hours since creation or last reaction)
  isPostExpired: (post) => {
    const now = new Date();
    const lastActivity = post.reactions && post.reactions.length > 0 
      ? new Date(Math.max(...post.reactions.map(r => new Date(r.created_at))))
      : new Date(post.created_at);
    
    const timeDiff = now - lastActivity;
    const hoursDiff = timeDiff / (1000 * 60 * 60);
    
    return hoursDiff >= 24;
  },

  // Get time remaining until post expires
  getTimeUntilExpiry: (post) => {
    const lastActivity = post.reactions && post.reactions.length > 0 
      ? new Date(Math.max(...post.reactions.map(r => new Date(r.created_at))))
      : new Date(post.created_at);
    
    const expiryTime = new Date(lastActivity.getTime() + (24 * 60 * 60 * 1000));
    const now = new Date();
    const timeRemaining = expiryTime - now;
    
    if (timeRemaining <= 0) return null;
    
    const hours = Math.floor(timeRemaining / (1000 * 60 * 60));
    const minutes = Math.floor((timeRemaining % (1000 * 60 * 60)) / (1000 * 60));
    
    return { hours, minutes, totalMinutes: Math.floor(timeRemaining / (1000 * 60)) };
  },

  // Format time remaining for display
  formatTimeRemaining: (timeRemaining) => {
    if (!timeRemaining) return 'Expired';
    
    const { hours, minutes } = timeRemaining;
    
    if (hours > 0) {
      return `${hours}h ${minutes}m left`;
    }
    
    return `${minutes}m left`;
  },

  // Get relative time string (e.g., "2 minutes ago")
  getRelativeTime: (timestamp) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffMs = now - time;
    
    const seconds = Math.floor(diffMs / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    
    if (days > 0) {
      return `${days}d ago`;
    } else if (hours > 0) {
      return `${hours}h ago`;
    } else if (minutes > 0) {
      return `${minutes}m ago`;
    } else {
      return 'Just now';
    }
  },

  // Check if it's a new day (for mood ping reset)
  isNewDay: (lastMoodUpdate) => {
    if (!lastMoodUpdate) return true;
    
    const today = new Date();
    const lastUpdate = new Date(lastMoodUpdate);
    
    return today.toDateString() !== lastUpdate.toDateString();
  },

  // Generate a timestamp for database insertion
  now: () => new Date().toISOString(),

  // Check if time is within the last X minutes (for real-time features)
  isWithinMinutes: (timestamp, minutes) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffMs = now - time;
    const diffMinutes = diffMs / (1000 * 60);
    
    return diffMinutes <= minutes;
  },
}; 