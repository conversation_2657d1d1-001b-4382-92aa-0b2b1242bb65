import { create } from 'zustand';
import { supabase } from '../services/supabaseClient';

export const useCircleStore = create((set, get) => ({
  // User state - Start unauthenticated
  user: null,
  isAuthenticated: false,
  
  // Circles state
  circles: [],
  activeCircle: null,
  
  // Posts state
  posts: [],
  
  // UI state
  isLoading: false,
  error: null,
  _hasEverFetchedCircles: false, // Emergency flag to prevent infinite loops

  // Auth actions
  setUser: (user) => set({ user, isAuthenticated: !!user }),
  
  clearUser: () => set({ user: null, isAuthenticated: false, circles: [], posts: [], activeCircle: null, _hasEverFetchedCircles: false }),

  // Circle actions
  setCircles: (circles) => set({ circles }),
  
  setActiveCircle: (circle) => set({ activeCircle: circle }),
  
  addCircle: (circle) => set((state) => ({ 
    circles: [...state.circles, circle] 
  })),
  
  removeCircle: (circleId) => set((state) => ({
    circles: state.circles.filter(c => c.id !== circleId),
    activeCircle: state.activeCircle?.id === circleId ? null : state.activeCircle
  })),

  // Posts actions
  setPosts: (posts) => set({ posts }),
  
  addPost: (post) => set((state) => ({ 
    posts: [post, ...state.posts] 
  })),
  
  removePost: (postId) => set((state) => ({
    posts: state.posts.filter(p => p.id !== postId)
  })),
  
  updatePost: (postId, updates) => set((state) => ({
    posts: state.posts.map(p => p.id === postId ? { ...p, ...updates } : p)
  })),

  // API actions
  fetchCircles: async () => {
    const state = get();

    // EMERGENCY: Prevent infinite loop - only allow one fetch per session
    if (state._hasEverFetchedCircles) {
      console.log('🛑 EMERGENCY: fetchCircles already called once, preventing infinite loop');
      return state.circles;
    }

    // Prevent multiple simultaneous calls
    if (state.isLoading) {
      console.log('⏸️ fetchCircles already in progress, skipping...');
      return state.circles;
    }

    try {
      console.log('🔄 Starting fetchCircles...');
      set({ isLoading: true, error: null, _hasEverFetchedCircles: true });
      const { user } = get();

      if (!user) {
        console.log('❌ No user found, skipping circle fetch');
        set({ circles: [], isLoading: false });
        return [];
      }

      console.log('👤 Fetching circles for user:', user.id);

      const { data, error } = await supabase
        .from('circle_members')
        .select(`
          circle_id,
          circles (
            id,
            name,
            emoji,
            created_at,
            creator_id
          )
        `)
        .eq('user_id', user.id);

      if (error) {
        console.log('❌ Supabase error in fetchCircles:', error);
        throw error;
      }

      console.log('📊 Raw circle data:', data);
      const circles = data ? data.map(item => item.circles).filter(circle => circle !== null) : [];
      console.log('🔒 Processed circles:', circles);

      set({ circles, isLoading: false });
      console.log('✅ fetchCircles completed successfully, found', circles.length, 'circles');

      return circles;
    } catch (error) {
      console.log('❌ Error in fetchCircles:', error.message);
      console.log('❌ Full error object:', error);
      set({ error: error.message, isLoading: false });
      return [];
    }
  },

  fetchPosts: async (circleId) => {
    try {
      set({ isLoading: true, error: null });
      
      const { data, error } = await supabase
        .from('posts')
        .select(`
          *,
          users (
            id,
            username,
            avatar_url
          ),
          reactions (
            id,
            type,
            content,
            user_id,
            created_at
          )
        `)
        .eq('circle_id', circleId)
        .order('created_at', { ascending: false });

      if (error) throw error;

      set({ posts: data, isLoading: false });
      return data;
    } catch (error) {
      console.log('Error in fetchPosts:', error.message);
      set({ error: error.message, isLoading: false });
      return [];
    }
  },

  createCircle: async (name, emoji) => {
    try {
      set({ isLoading: true, error: null });
      const { user } = get();
      
      if (!user) {
        throw new Error('User not authenticated');
      }

      const { data: circle, error: circleError } = await supabase
        .from('circles')
        .insert({
          name,
          emoji,
          creator_id: user.id,
        })
        .select()
        .single();

      if (circleError) throw circleError;

      // Add creator as member
      const { error: memberError } = await supabase
        .from('circle_members')
        .insert({
          circle_id: circle.id,
          user_id: user.id,
          role: 'creator',
        });

      if (memberError) throw memberError;

      set((state) => ({ 
        circles: [...state.circles, circle],
        isLoading: false 
      }));
      
      return circle;
    } catch (error) {
      console.log('Error in createCircle:', error.message);
      set({ error: error.message, isLoading: false });
      throw error;
    }
  },

  joinCircle: async (inviteCode) => {
    try {
      set({ isLoading: true, error: null });
      const { user } = get();
      
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Find circle by invite code
      const { data: circle, error: circleError } = await supabase
        .from('circles')
        .select('*')
        .eq('invite_code', inviteCode)
        .single();

      if (circleError) throw circleError;

      // Check if user is already a member
      const { data: existingMember } = await supabase
        .from('circle_members')
        .select('id')
        .eq('circle_id', circle.id)
        .eq('user_id', user.id)
        .single();

      if (existingMember) {
        throw new Error('You are already a member of this circle');
      }

      // Add user as member
      const { error: memberError } = await supabase
        .from('circle_members')
        .insert({
          circle_id: circle.id,
          user_id: user.id,
          role: 'member',
        });

      if (memberError) throw memberError;

      set((state) => ({ 
        circles: [...state.circles, circle],
        isLoading: false 
      }));
      
      return circle;
    } catch (error) {
      console.log('Error in joinCircle:', error.message);
      set({ error: error.message, isLoading: false });
      throw error;
    }
  },

  // UI state actions
  setLoading: (isLoading) => set({ isLoading }),
  setError: (error) => set({ error }),
  clearError: () => set({ error: null }),
})); 