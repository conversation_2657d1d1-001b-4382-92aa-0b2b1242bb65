import { create } from 'zustand';
import { supabase } from '../services/supabaseClient';

export const useCircleStore = create((set, get) => ({
  // User state - Start unauthenticated
  user: null,
  isAuthenticated: false,
  
  // Circles state
  circles: [],
  activeCircle: null,
  
  // Posts state
  posts: [],
  
  // UI state
  isLoading: false,
  error: null,

  // Auth actions
  setUser: (user) => set({ user, isAuthenticated: !!user }),
  
  clearUser: () => set({ user: null, isAuthenticated: false, circles: [], posts: [], activeCircle: null }),

  // Circle actions
  setCircles: (circles) => set({ circles }),
  
  setActiveCircle: (circle) => set({ activeCircle: circle }),
  
  addCircle: (circle) => set((state) => ({ 
    circles: [...state.circles, circle] 
  })),
  
  removeCircle: (circleId) => set((state) => ({
    circles: state.circles.filter(c => c.id !== circleId),
    activeCircle: state.activeCircle?.id === circleId ? null : state.activeCircle
  })),

  // Posts actions
  setPosts: (posts) => set({ posts }),
  
  addPost: (post) => set((state) => ({ 
    posts: [post, ...state.posts] 
  })),
  
  removePost: (postId) => set((state) => ({
    posts: state.posts.filter(p => p.id !== postId)
  })),
  
  updatePost: (postId, updates) => set((state) => ({
    posts: state.posts.map(p => p.id === postId ? { ...p, ...updates } : p)
  })),

  // API actions
  fetchCircles: async () => {
    try {
      set({ isLoading: true, error: null });
      const { user } = get();
      
      if (!user) {
        console.log('No user found, skipping circle fetch');
        set({ circles: [], isLoading: false });
        return [];
      }

      const { data, error } = await supabase
        .from('circle_members')
        .select(`
          circle_id,
          circles (
            id,
            name,
            emoji,
            created_at,
            creator_id
          )
        `)
        .eq('user_id', user.id);

      if (error) throw error;

      const circles = data.map(item => item.circles);
      set({ circles, isLoading: false });
      
      return circles;
    } catch (error) {
      console.log('Error in fetchCircles:', error.message);
      set({ error: error.message, isLoading: false });
      return [];
    }
  },

  fetchPosts: async (circleId) => {
    try {
      set({ isLoading: true, error: null });
      
      const { data, error } = await supabase
        .from('posts')
        .select(`
          *,
          users (
            id,
            username,
            avatar_url
          ),
          reactions (
            id,
            type,
            content,
            user_id,
            created_at
          )
        `)
        .eq('circle_id', circleId)
        .order('created_at', { ascending: false });

      if (error) throw error;

      set({ posts: data, isLoading: false });
      return data;
    } catch (error) {
      console.log('Error in fetchPosts:', error.message);
      set({ error: error.message, isLoading: false });
      return [];
    }
  },

  createCircle: async (name, emoji) => {
    try {
      set({ isLoading: true, error: null });
      const { user } = get();
      
      if (!user) {
        throw new Error('User not authenticated');
      }

      const { data: circle, error: circleError } = await supabase
        .from('circles')
        .insert({
          name,
          emoji,
          creator_id: user.id,
        })
        .select()
        .single();

      if (circleError) throw circleError;

      // Add creator as member
      const { error: memberError } = await supabase
        .from('circle_members')
        .insert({
          circle_id: circle.id,
          user_id: user.id,
          role: 'creator',
        });

      if (memberError) throw memberError;

      set((state) => ({ 
        circles: [...state.circles, circle],
        isLoading: false 
      }));
      
      return circle;
    } catch (error) {
      console.log('Error in createCircle:', error.message);
      set({ error: error.message, isLoading: false });
      throw error;
    }
  },

  joinCircle: async (inviteCode) => {
    try {
      set({ isLoading: true, error: null });
      const { user } = get();
      
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Find circle by invite code
      const { data: circle, error: circleError } = await supabase
        .from('circles')
        .select('*')
        .eq('invite_code', inviteCode)
        .single();

      if (circleError) throw circleError;

      // Check if user is already a member
      const { data: existingMember } = await supabase
        .from('circle_members')
        .select('id')
        .eq('circle_id', circle.id)
        .eq('user_id', user.id)
        .single();

      if (existingMember) {
        throw new Error('You are already a member of this circle');
      }

      // Add user as member
      const { error: memberError } = await supabase
        .from('circle_members')
        .insert({
          circle_id: circle.id,
          user_id: user.id,
          role: 'member',
        });

      if (memberError) throw memberError;

      set((state) => ({ 
        circles: [...state.circles, circle],
        isLoading: false 
      }));
      
      return circle;
    } catch (error) {
      console.log('Error in joinCircle:', error.message);
      set({ error: error.message, isLoading: false });
      throw error;
    }
  },

  // UI state actions
  setLoading: (isLoading) => set({ isLoading }),
  setError: (error) => set({ error }),
  clearError: () => set({ error: null }),
})); 