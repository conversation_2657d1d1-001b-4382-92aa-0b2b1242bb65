import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Alert,
  Image,
  Dimensions,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Camera } from 'expo-camera';
import * as ImagePicker from 'expo-image-picker';
import { Audio } from 'expo-av';
import { useCircleStore } from '../store/circleStore';
import { permissions } from '../utils/permissions';

const { width, height } = Dimensions.get('window');

const PostCreationScreen = ({ navigation }) => {
  const [postType, setPostType] = useState('camera'); // camera, text, voice, gallery
  const [textContent, setTextContent] = useState('');
  const [selectedImage, setSelectedImage] = useState(null);
  const [isRecording, setIsRecording] = useState(false);
  const [recordingDuration, setRecordingDuration] = useState(0);
  const [audioRecording, setAudioRecording] = useState(null);
  const [cameraRef, setCameraRef] = useState(null);
  const [cameraType, setCameraType] = useState(Camera.Constants.Type.back);
  const [flashMode, setFlashMode] = useState(Camera.Constants.FlashMode.off);
  const [isUploading, setIsUploading] = useState(false);

  const { activeCircle, addPost, user } = useCircleStore();
  const recordingTimer = useRef(null);

  useEffect(() => {
    requestPermissions();
    
    return () => {
      if (recordingTimer.current) {
        clearInterval(recordingTimer.current);
      }
      if (audioRecording) {
        audioRecording.unloadAsync();
      }
    };
  }, []);

  const requestPermissions = async () => {
    await permissions.requestCameraPermission();
    await permissions.requestAudioPermission();
    await permissions.requestMediaLibraryPermission();
  };

  const startRecordingTimer = () => {
    recordingTimer.current = setInterval(() => {
      setRecordingDuration(prev => {
        if (prev >= 10) {
          stopVoiceRecording();
          return 10;
        }
        return prev + 1;
      });
    }, 1000);
  };

  const stopRecordingTimer = () => {
    if (recordingTimer.current) {
      clearInterval(recordingTimer.current);
      recordingTimer.current = null;
    }
  };

  const startVoiceRecording = async () => {
    try {
      const hasPermission = await permissions.checkAudioPermission();
      if (!hasPermission) {
        Alert.alert('Permission Required', 'Please grant microphone permission to record voice notes.');
        return;
      }

      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
      });

      const recording = new Audio.Recording();
      await recording.prepareToRecordAsync(Audio.RECORDING_OPTIONS_PRESET_HIGH_QUALITY);
      await recording.startAsync();

      setAudioRecording(recording);
      setIsRecording(true);
      setRecordingDuration(0);
      startRecordingTimer();
    } catch (error) {
      console.error('Failed to start recording:', error);
      Alert.alert('Error', 'Failed to start recording');
    }
  };

  const stopVoiceRecording = async () => {
    try {
      if (audioRecording) {
        setIsRecording(false);
        stopRecordingTimer();
        
        await audioRecording.stopAndUnloadAsync();
        const uri = audioRecording.getURI();
        
        // TODO: Upload audio file and create post
        console.log('Audio recorded:', uri);
        Alert.alert('Voice Note Recorded', `Duration: ${recordingDuration}s`);
        
        setAudioRecording(null);
        setRecordingDuration(0);
      }
    } catch (error) {
      console.error('Failed to stop recording:', error);
      Alert.alert('Error', 'Failed to stop recording');
    }
  };

  const takePicture = async () => {
    if (cameraRef) {
      try {
        const photo = await cameraRef.takePictureAsync({
          quality: 0.8,
          base64: false,
        });
        setSelectedImage(photo.uri);
        setPostType('preview');
      } catch (error) {
        console.error('Failed to take picture:', error);
        Alert.alert('Error', 'Failed to take picture');
      }
    }
  };

  const recordVideo = async () => {
    if (cameraRef) {
      try {
        const video = await cameraRef.recordAsync({
          quality: Camera.Constants.VideoQuality['720p'],
          maxDuration: 15,
        });
        setSelectedImage(video.uri);
        setPostType('preview');
      } catch (error) {
        console.error('Failed to record video:', error);
        Alert.alert('Error', 'Failed to record video');
      }
    }
  };

  const pickFromGallery = async () => {
    try {
      const hasPermission = await permissions.checkMediaLibraryPermission();
      if (!hasPermission) {
        Alert.alert('Permission Required', 'Please grant photo library permission to select media.');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.All,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
        videoMaxDuration: 15,
      });

      if (!result.canceled && result.assets[0]) {
        setSelectedImage(result.assets[0].uri);
        setPostType('preview');
      }
    } catch (error) {
      console.error('Failed to pick from gallery:', error);
      Alert.alert('Error', 'Failed to select media');
    }
  };

  const handlePostSubmit = async () => {
    if (!activeCircle) {
      Alert.alert('Error', 'Please select a circle first');
      return;
    }

    if (postType === 'text' && !textContent.trim()) {
      Alert.alert('Error', 'Please enter some text');
      return;
    }

    if ((postType === 'preview' || postType === 'camera') && !selectedImage) {
      Alert.alert('Error', 'Please select or capture media');
      return;
    }

    try {
      setIsUploading(true);

      // Create mock post data
      const newPost = {
        id: Date.now().toString(),
        circle_id: activeCircle.id,
        user_id: user.id,
        type: postType === 'text' ? 'text' : selectedImage?.includes('.mp4') ? 'video' : 'image',
        content: postType === 'text' ? textContent : null,
        media_url: selectedImage || null,
        created_at: new Date().toISOString(),
        reactions: [],
        users: {
          id: user.id,
          username: user.username,
          avatar_url: null,
        },
      };

      // TODO: Implement actual upload to Supabase
      addPost(newPost);

      Alert.alert('Success', 'Post shared with your circle!', [
        { text: 'OK', onPress: () => navigation.navigate('Circles') }
      ]);

      // Reset form
      setTextContent('');
      setSelectedImage(null);
      setPostType('camera');
    } catch (error) {
      console.error('Failed to create post:', error);
      Alert.alert('Error', 'Failed to share post');
    } finally {
      setIsUploading(false);
    }
  };

  const renderTypeSelector = () => (
    <View style={styles.typeSelector}>
      <TouchableOpacity
        style={[styles.typeButton, postType === 'camera' && styles.activeType]}
        onPress={() => setPostType('camera')}
      >
        <Text style={styles.typeEmoji}>📸</Text>
        <Text style={[styles.typeText, postType === 'camera' && styles.activeTypeText]}>Camera</Text>
      </TouchableOpacity>
      
      <TouchableOpacity
        style={[styles.typeButton, postType === 'gallery' && styles.activeType]}
        onPress={() => {
          setPostType('gallery');
          pickFromGallery();
        }}
      >
        <Text style={styles.typeEmoji}>🖼️</Text>
        <Text style={[styles.typeText, postType === 'gallery' && styles.activeTypeText]}>Gallery</Text>
      </TouchableOpacity>
      
      <TouchableOpacity
        style={[styles.typeButton, postType === 'text' && styles.activeType]}
        onPress={() => setPostType('text')}
      >
        <Text style={styles.typeEmoji}>✏️</Text>
        <Text style={[styles.typeText, postType === 'text' && styles.activeTypeText]}>Text</Text>
      </TouchableOpacity>
      
      <TouchableOpacity
        style={[styles.typeButton, postType === 'voice' && styles.activeType]}
        onPress={() => setPostType('voice')}
      >
        <Text style={styles.typeEmoji}>🎙️</Text>
        <Text style={[styles.typeText, postType === 'voice' && styles.activeTypeText]}>Voice</Text>
      </TouchableOpacity>
    </View>
  );

  const renderCamera = () => (
    <View style={styles.cameraContainer}>
      <Camera
        style={styles.camera}
        type={cameraType}
        flashMode={flashMode}
        ref={ref => setCameraRef(ref)}
      >
        <View style={styles.cameraOverlay}>
          <View style={styles.cameraTopControls}>
            <TouchableOpacity
              style={styles.cameraControl}
              onPress={() => navigation.goBack()}
            >
              <Text style={styles.cameraControlText}>✕</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.cameraControl}
              onPress={() => setFlashMode(
                flashMode === Camera.Constants.FlashMode.off
                  ? Camera.Constants.FlashMode.on
                  : Camera.Constants.FlashMode.off
              )}
            >
              <Text style={styles.cameraControlText}>
                {flashMode === Camera.Constants.FlashMode.off ? '⚡' : '⚡️'}
              </Text>
            </TouchableOpacity>
          </View>
          
          <View style={styles.cameraBottomControls}>
            <TouchableOpacity
              style={styles.galleryButton}
              onPress={pickFromGallery}
            >
              <Text style={styles.galleryButtonText}>📷</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.captureButton}
              onPress={takePicture}
            >
              <View style={styles.captureButtonInner} />
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.flipButton}
              onPress={() => setCameraType(
                cameraType === Camera.Constants.Type.back
                  ? Camera.Constants.Type.front
                  : Camera.Constants.Type.back
              )}
            >
              <Text style={styles.flipButtonText}>🔄</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Camera>
    </View>
  );

  const renderTextInput = () => (
    <View style={styles.textContainer}>
      <Text style={styles.inputLabel}>What's on your mind?</Text>
      <TextInput
        style={styles.textInput}
        placeholder="Share your thoughts with the circle..."
        placeholderTextColor="#AAAAAA"
        value={textContent}
        onChangeText={setTextContent}
        multiline
        maxLength={500}
        textAlignVertical="top"
      />
      <Text style={styles.characterCount}>{textContent.length}/500</Text>
    </View>
  );

  const renderVoiceRecorder = () => (
    <View style={styles.voiceContainer}>
      <Text style={styles.voiceTitle}>Voice Note</Text>
      <Text style={styles.voiceSubtitle}>Record up to 10 seconds</Text>
      
      <View style={styles.voiceRecorder}>
        <TouchableOpacity
          style={[styles.recordButton, isRecording && styles.recordingButton]}
          onPress={isRecording ? stopVoiceRecording : startVoiceRecording}
        >
          <Text style={styles.recordButtonText}>
            {isRecording ? '⏹️' : '🎙️'}
          </Text>
        </TouchableOpacity>
        
        {isRecording && (
          <View style={styles.recordingInfo}>
            <Text style={styles.recordingTime}>{recordingDuration}s</Text>
            <View style={styles.waveform}>
              {[...Array(5)].map((_, i) => (
                <View
                  key={i}
                  style={[
                    styles.waveBar,
                    { height: Math.random() * 20 + 10 }
                  ]}
                />
              ))}
            </View>
          </View>
        )}
      </View>
    </View>
  );

  const renderPreview = () => (
    <View style={styles.previewContainer}>
      <Image source={{ uri: selectedImage }} style={styles.previewImage} />
      <TouchableOpacity
        style={styles.retakeButton}
        onPress={() => {
          setSelectedImage(null);
          setPostType('camera');
        }}
      >
        <Text style={styles.retakeButtonText}>Retake</Text>
      </TouchableOpacity>
    </View>
  );

  const renderContent = () => {
    switch (postType) {
      case 'camera':
        return renderCamera();
      case 'text':
        return renderTextInput();
      case 'voice':
        return renderVoiceRecorder();
      case 'preview':
        return renderPreview();
      default:
        return renderCamera();
    }
  };

  return (
    <LinearGradient
      colors={['#0D0D0D', '#1A1A1A']}
      style={styles.container}
    >
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.backButtonText}>←</Text>
          </TouchableOpacity>
          
          <Text style={styles.headerTitle}>Create Post</Text>
          
          {activeCircle && (
            <Text style={styles.circleIndicator}>
              {activeCircle.emoji} {activeCircle.name}
            </Text>
          )}
        </View>

        {postType !== 'camera' && postType !== 'preview' && renderTypeSelector()}

        <View style={styles.content}>
          {renderContent()}
        </View>

        {(postType === 'text' || postType === 'preview' || (postType === 'voice' && !isRecording)) && (
          <View style={styles.footer}>
            <TouchableOpacity
              style={styles.shareButton}
              onPress={handlePostSubmit}
              disabled={isUploading}
            >
              <LinearGradient
                colors={['#7C4DFF', '#18FFFF']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.gradientButton}
              >
                <Text style={styles.shareButtonText}>
                  {isUploading ? 'Sharing...' : 'Share with Circle'}
                </Text>
              </LinearGradient>
            </TouchableOpacity>
          </View>
        )}
      </SafeAreaView>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#333333',
  },
  backButton: {
    padding: 8,
  },
  backButtonText: {
    fontSize: 24,
    color: '#FFFFFF',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  circleIndicator: {
    fontSize: 14,
    color: '#7C4DFF',
    fontWeight: '600',
  },
  typeSelector: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#333333',
  },
  typeButton: {
    alignItems: 'center',
    padding: 12,
    borderRadius: 12,
    minWidth: 60,
  },
  activeType: {
    backgroundColor: '#7C4DFF',
  },
  typeEmoji: {
    fontSize: 24,
    marginBottom: 4,
  },
  typeText: {
    fontSize: 12,
    color: '#AAAAAA',
    fontWeight: '600',
  },
  activeTypeText: {
    color: '#FFFFFF',
  },
  content: {
    flex: 1,
  },
  cameraContainer: {
    flex: 1,
  },
  camera: {
    flex: 1,
  },
  cameraOverlay: {
    flex: 1,
    justifyContent: 'space-between',
    padding: 20,
  },
  cameraTopControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  cameraControl: {
    backgroundColor: 'rgba(0,0,0,0.5)',
    borderRadius: 25,
    width: 50,
    height: 50,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cameraControlText: {
    fontSize: 20,
    color: '#FFFFFF',
  },
  cameraBottomControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  galleryButton: {
    backgroundColor: 'rgba(0,0,0,0.5)',
    borderRadius: 25,
    width: 50,
    height: 50,
    alignItems: 'center',
    justifyContent: 'center',
  },
  galleryButtonText: {
    fontSize: 24,
  },
  captureButton: {
    backgroundColor: '#FFFFFF',
    borderRadius: 40,
    width: 80,
    height: 80,
    alignItems: 'center',
    justifyContent: 'center',
  },
  captureButtonInner: {
    backgroundColor: '#FFFFFF',
    borderRadius: 35,
    width: 70,
    height: 70,
    borderWidth: 3,
    borderColor: '#000000',
  },
  flipButton: {
    backgroundColor: 'rgba(0,0,0,0.5)',
    borderRadius: 25,
    width: 50,
    height: 50,
    alignItems: 'center',
    justifyContent: 'center',
  },
  flipButtonText: {
    fontSize: 24,
  },
  textContainer: {
    flex: 1,
    padding: 20,
  },
  inputLabel: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 16,
  },
  textInput: {
    backgroundColor: '#2A2A2A',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: '#FFFFFF',
    minHeight: 200,
    marginBottom: 8,
  },
  characterCount: {
    fontSize: 12,
    color: '#AAAAAA',
    textAlign: 'right',
  },
  voiceContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  voiceTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  voiceSubtitle: {
    fontSize: 16,
    color: '#AAAAAA',
    marginBottom: 40,
  },
  voiceRecorder: {
    alignItems: 'center',
  },
  recordButton: {
    backgroundColor: '#7C4DFF',
    borderRadius: 50,
    width: 100,
    height: 100,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
  },
  recordingButton: {
    backgroundColor: '#FF4081',
  },
  recordButtonText: {
    fontSize: 40,
  },
  recordingInfo: {
    alignItems: 'center',
  },
  recordingTime: {
    fontSize: 18,
    color: '#FFFFFF',
    fontWeight: 'bold',
    marginBottom: 12,
  },
  waveform: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 40,
  },
  waveBar: {
    backgroundColor: '#7C4DFF',
    width: 4,
    marginHorizontal: 2,
    borderRadius: 2,
  },
  previewContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  previewImage: {
    width: width - 40,
    height: (width - 40) * 1.33,
    borderRadius: 12,
    marginBottom: 20,
  },
  retakeButton: {
    backgroundColor: '#2A2A2A',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 24,
  },
  retakeButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  footer: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#333333',
  },
  shareButton: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  gradientButton: {
    paddingVertical: 16,
    alignItems: 'center',
  },
  shareButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default PostCreationScreen; 