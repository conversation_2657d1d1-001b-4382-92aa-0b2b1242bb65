import React, { useEffect } from 'react';
import { StatusBar } from 'expo-status-bar';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { View, Text, StyleSheet } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useCircleStore } from './store/circleStore';
import { auth } from './services/supabaseClient';

// Import screens (will create these next)
import OnboardingScreen from './screens/OnboardingScreen';
import CircleHomeScreen from './screens/CircleHomeScreen';
import PostCreationScreen from './screens/PostCreationScreen';
import MemoryVaultScreen from './screens/MemoryVaultScreen';
import SettingsScreen from './screens/SettingsScreen';

const Stack = createStackNavigator();
const Tab = createBottomTabNavigator();

// Temporary loading screen
const LoadingScreen = () => (
  <View style={styles.container}>
    <LinearGradient
      colors={['#0D0D0D', '#1A1A1A']}
      style={styles.background}
    >
      <Text style={styles.loadingText}>Loading Secret Circle...</Text>
    </LinearGradient>
  </View>
);

// Main tab navigator for authenticated users
const MainTabNavigator = () => (
  <Tab.Navigator
    screenOptions={{
      headerShown: false,
      tabBarStyle: {
        backgroundColor: '#1A1A1A',
        borderTopColor: '#333333',
        borderTopWidth: 1,
      },
      tabBarActiveTintColor: '#7C4DFF',
      tabBarInactiveTintColor: '#AAAAAA',
    }}
  >
    <Tab.Screen 
      name="Circles" 
      component={CircleHomeScreen}
      options={{
        tabBarIcon: ({ color }) => <Text style={{ color, fontSize: 20 }}>🔒</Text>
      }}
    />
    <Tab.Screen 
      name="Create" 
      component={PostCreationScreen}
      options={{
        tabBarIcon: ({ color }) => <Text style={{ color, fontSize: 20 }}>➕</Text>
      }}
    />
    <Tab.Screen 
      name="Vault" 
      component={MemoryVaultScreen}
      options={{
        tabBarIcon: ({ color }) => <Text style={{ color, fontSize: 20 }}>💾</Text>
      }}
    />
    <Tab.Screen 
      name="Settings" 
      component={SettingsScreen}
      options={{
        tabBarIcon: ({ color }) => <Text style={{ color, fontSize: 20 }}>⚙️</Text>
      }}
    />
  </Tab.Navigator>
);

// Main stack navigator
const RootNavigator = () => {
  const isAuthenticated = useCircleStore(state => state.isAuthenticated);
  const circles = useCircleStore(state => state.circles);
  
  const needsOnboarding = !isAuthenticated || (isAuthenticated && circles.length === 0);
  
  return (
    <Stack.Navigator 
      screenOptions={{ 
        headerShown: false,
        cardStyle: { backgroundColor: '#0D0D0D' }
      }}
    >
      {needsOnboarding ? (
        <Stack.Screen name="Onboarding" component={OnboardingScreen} />
      ) : (
        <Stack.Screen name="Main" component={MainTabNavigator} />
      )}
    </Stack.Navigator>
  );
};

export default function App() {
  const { setUser, clearUser, isLoading, setLoading, fetchCircles } = useCircleStore();

  useEffect(() => {
    let mounted = true;
    let hasInitialized = false;

    // Initialize authentication state
    const initAuth = async () => {
      if (hasInitialized) return;
      hasInitialized = true;

      try {
        console.log('🚀 Initializing authentication...');
        setLoading(true);

        // Check if user is already logged in
        const { data: { user }, error } = await auth.getCurrentUser();

        if (mounted) {
          if (error && error.name !== 'AuthSessionMissingError') {
            console.error('Error getting user:', error);
            clearUser();
          } else if (user) {
            console.log('User already authenticated:', user.email);
            setUser(user);
            // Fetch circles for already authenticated user
            try {
              await fetchCircles();
            } catch (error) {
              console.log('Error fetching circles for authenticated user:', error);
            }
          } else {
            console.log('No authenticated user found');
            clearUser();
          }
          // Always clear loading here
          setLoading(false);
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        if (mounted) {
          clearUser();
          setLoading(false);
        }
      }
    };

    initAuth();

    // Listen for auth state changes
    const { data: { subscription } } = auth.onAuthStateChange(async (event, session) => {
      console.log('Auth state changed:', event, session?.user?.email);

      if (mounted && hasInitialized) {
        if (event === 'SIGNED_IN' && session?.user) {
          console.log('User signed in successfully');
          setUser(session.user);
          // Always fetch circles after sign-in
          try {
            console.log('🔄 About to call fetchCircles after SIGNED_IN...');
            await fetchCircles();
            console.log('✅ fetchCircles call completed after SIGNED_IN');
          } catch (error) {
            console.log('❌ Error fetching circles after sign in:', error);
          }
        } else if (event === 'SIGNED_OUT') {
          console.log('User signed out');
          clearUser();
        } else if (event === 'INITIAL_SESSION') {
          // Handle initial session load - but don't fetch circles again
          if (session?.user) {
            console.log('Setting user from INITIAL_SESSION');
            setUser(session.user);
            // Don't fetch circles here as it's already done in initAuth
          } else {
            clearUser();
          }
        }
        // Always clear loading after auth state change
        setLoading(false);
      }
    });

    return () => {
      mounted = false;
      subscription?.unsubscribe();
    };
  }, [setUser, clearUser, setLoading, fetchCircles]);

  if (isLoading) {
    return <LoadingScreen />;
  }

  return (
    <NavigationContainer>
      <RootNavigator />
      <StatusBar style="light" />
    </NavigationContainer>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    fontSize: 18,
    color: '#7C4DFF',
    fontWeight: '600',
  },
});



