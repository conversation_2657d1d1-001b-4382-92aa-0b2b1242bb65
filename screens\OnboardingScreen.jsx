import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useCircleStore } from '../store/circleStore';
import { auth } from '../services/supabaseClient';

const OnboardingScreen = () => {
  const [currentStep, setCurrentStep] = useState('welcome'); // welcome, auth, createOrJoin, createCircle, joinCircle
  const [authMode, setAuthMode] = useState('login'); // login, signup
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [username, setUsername] = useState('');
  const [circleName, setCircleName] = useState('');
  const [selectedEmoji, setSelectedEmoji] = useState('🔥');
  const [inviteCode, setInviteCode] = useState('');
  const [authLoading, setAuthLoading] = useState(false);

  const { user, isAuthenticated, setUser, createCircle, joinCircle, isLoading, fetchCircles } = useCircleStore();

  const circleEmojis = ['🔥', '⭐', '🌙', '🎉', '💫', '🌟', '✨', '🎭', '🎨', '🌈'];

  // Handle when authenticated user with no circles comes to onboarding
  React.useEffect(() => {
    if (isAuthenticated && user && currentStep === 'welcome') {
      setCurrentStep('createOrJoin');
    }
  }, [isAuthenticated, user, currentStep]);

  const handleAuth = async () => {
    if (!email || !password || (authMode === 'signup' && !username)) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    try {
      setAuthLoading(true);
      
      if (authMode === 'signup') {
        console.log('Attempting signup for:', email);
        const { data, error } = await auth.signUp(email, password);
        
        if (error) throw error;
        
        if (data.user) {
          console.log('Signup successful:', data.user.email);
          // For new users, always show create/join options
          setTimeout(() => {
            setCurrentStep('createOrJoin');
          }, 100);
        } else {
          Alert.alert('Signup', 'Please check your email to confirm your account');
        }
      } else {
        console.log('Attempting login for:', email);
        const { data, error } = await auth.signIn(email, password);
        
        if (error) throw error;
        
        if (data.user) {
          console.log('Login successful:', data.user.email);
          // The auth state change will be handled by App.js
          // Just let the authentication flow complete naturally
        }
      }
    } catch (error) {
      console.error('Auth error:', error);
      Alert.alert('Authentication Failed', error.message);
    } finally {
      setAuthLoading(false);
    }
  };

  const handleCreateCircle = async () => {
    if (!circleName.trim()) {
      Alert.alert('Error', 'Please enter a circle name');
      return;
    }

    try {
      console.log('About to create circle, checking user state...');
      const storeState = useCircleStore.getState();
      console.log('Current user in store:', storeState.user);
      console.log('Is authenticated:', storeState.isAuthenticated);
      
      await createCircle(circleName.trim(), selectedEmoji);
      Alert.alert('Success', 'Circle created! You can now invite friends.');
    } catch (error) {
      console.error('Create circle error:', error);
      Alert.alert('Error', error.message);
    }
  };

  const handleJoinCircle = async () => {
    if (!inviteCode.trim()) {
      Alert.alert('Error', 'Please enter an invite code');
      return;
    }

    try {
      await joinCircle(inviteCode.trim());
      Alert.alert('Success', 'Successfully joined the circle!');
    } catch (error) {
      Alert.alert('Error', error.message);
    }
  };

  const renderWelcome = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.title}>Welcome to</Text>
      <Text style={styles.brandTitle}>Secret Circle</Text>
      <Text style={styles.subtitle}>
        Private social app for close friends
      </Text>
      
      <View style={styles.featureHighlights}>
        <Text style={styles.featureText}>🔒 Private friend circles</Text>
        <Text style={styles.featureText}>⏰ Disappearing content</Text>
        <Text style={styles.featureText}>😍 Voice & video reactions</Text>
      </View>

      <TouchableOpacity 
        style={styles.primaryButton} 
        onPress={() => setCurrentStep('auth')}
      >
        <LinearGradient
          colors={['#7C4DFF', '#18FFFF']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={styles.gradientButton}
        >
          <Text style={styles.buttonText}>Get Started</Text>
        </LinearGradient>
      </TouchableOpacity>
    </View>
  );

  const renderAuth = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>
        {authMode === 'login' ? 'Welcome Back' : 'Create Account'}
      </Text>
      
      <View style={styles.authToggle}>
        <TouchableOpacity 
          style={[styles.toggleButton, authMode === 'login' && styles.activeToggle]}
          onPress={() => setAuthMode('login')}
        >
          <Text style={[styles.toggleText, authMode === 'login' && styles.activeToggleText]}>
            Log In
          </Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.toggleButton, authMode === 'signup' && styles.activeToggle]}
          onPress={() => setAuthMode('signup')}
        >
          <Text style={[styles.toggleText, authMode === 'signup' && styles.activeToggleText]}>
            Sign Up
          </Text>
        </TouchableOpacity>
      </View>

      {authMode === 'signup' && (
        <TextInput
          style={styles.input}
          placeholder="Username"
          placeholderTextColor="#AAAAAA"
          value={username}
          onChangeText={setUsername}
          autoCapitalize="none"
        />
      )}
      
      <TextInput
        style={styles.input}
        placeholder="Email"
        placeholderTextColor="#AAAAAA"
        value={email}
        onChangeText={setEmail}
        keyboardType="email-address"
        autoCapitalize="none"
      />
      
      <TextInput
        style={styles.input}
        placeholder="Password"
        placeholderTextColor="#AAAAAA"
        value={password}
        onChangeText={setPassword}
        secureTextEntry
      />

      <TouchableOpacity 
        style={styles.primaryButton} 
        onPress={handleAuth}
        disabled={authLoading}
      >
        <LinearGradient
          colors={['#7C4DFF', '#18FFFF']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={styles.gradientButton}
        >
          <Text style={styles.buttonText}>
            {authLoading ? 'Loading...' : (authMode === 'login' ? 'Log In' : 'Sign Up')}
          </Text>
        </LinearGradient>
      </TouchableOpacity>
    </View>
  );

  const renderCreateOrJoin = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Ready to Circle Up?</Text>
      <Text style={styles.stepSubtitle}>
        Create your own circle or join an existing one
      </Text>

      <TouchableOpacity 
        style={styles.optionButton} 
        onPress={() => setCurrentStep('createCircle')}
      >
        <Text style={styles.optionEmoji}>🆕</Text>
        <Text style={styles.optionTitle}>Create Circle</Text>
        <Text style={styles.optionDescription}>Start your own private group</Text>
      </TouchableOpacity>

      <TouchableOpacity 
        style={styles.optionButton} 
        onPress={() => setCurrentStep('joinCircle')}
      >
        <Text style={styles.optionEmoji}>🔗</Text>
        <Text style={styles.optionTitle}>Join Circle</Text>
        <Text style={styles.optionDescription}>Use an invite code from a friend</Text>
      </TouchableOpacity>
    </View>
  );

  const renderCreateCircle = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Create Your Circle</Text>
      
      <View style={styles.inputGroup}>
        <Text style={styles.inputLabel}>Circle Name</Text>
        <TextInput
          style={styles.input}
          placeholder="My Awesome Circle"
          placeholderTextColor="#AAAAAA"
          value={circleName}
          onChangeText={setCircleName}
          maxLength={30}
        />
      </View>

      <View style={styles.inputGroup}>
        <Text style={styles.inputLabel}>Choose an Emoji</Text>
        <View style={styles.emojiGrid}>
          {circleEmojis.map((emoji) => (
            <TouchableOpacity
              key={emoji}
              style={[
                styles.emojiOption,
                selectedEmoji === emoji && styles.selectedEmoji
              ]}
              onPress={() => setSelectedEmoji(emoji)}
            >
              <Text style={styles.emojiText}>{emoji}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      <View style={styles.buttonGroup}>
        <TouchableOpacity 
          style={styles.secondaryButton} 
          onPress={() => setCurrentStep('createOrJoin')}
        >
          <Text style={styles.secondaryButtonText}>Back</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.primaryButton, { flex: 1, marginLeft: 12 }]} 
          onPress={handleCreateCircle}
          disabled={isLoading}
        >
          <LinearGradient
            colors={['#7C4DFF', '#18FFFF']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={styles.gradientButton}
          >
            <Text style={styles.buttonText}>
              {isLoading ? 'Creating...' : 'Create Circle'}
            </Text>
          </LinearGradient>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderJoinCircle = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Join a Circle</Text>
      <Text style={styles.stepSubtitle}>
        Enter the invite code shared by your friend
      </Text>
      
      <View style={styles.inputGroup}>
        <Text style={styles.inputLabel}>Invite Code</Text>
        <TextInput
          style={styles.input}
          placeholder="ABC123"
          placeholderTextColor="#AAAAAA"
          value={inviteCode}
          onChangeText={setInviteCode}
          autoCapitalize="characters"
        />
      </View>

      <View style={styles.buttonGroup}>
        <TouchableOpacity 
          style={styles.secondaryButton} 
          onPress={() => setCurrentStep('createOrJoin')}
        >
          <Text style={styles.secondaryButtonText}>Back</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.primaryButton, { flex: 1, marginLeft: 12 }]} 
          onPress={handleJoinCircle}
          disabled={isLoading}
        >
          <LinearGradient
            colors={['#7C4DFF', '#18FFFF']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={styles.gradientButton}
          >
            <Text style={styles.buttonText}>
              {isLoading ? 'Joining...' : 'Join Circle'}
            </Text>
          </LinearGradient>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 'welcome':
        return renderWelcome();
      case 'auth':
        return renderAuth();
      case 'createOrJoin':
        return renderCreateOrJoin();
      case 'createCircle':
        return renderCreateCircle();
      case 'joinCircle':
        return renderJoinCircle();
      default:
        return renderWelcome();
    }
  };

  return (
    <LinearGradient
      colors={['#0D0D0D', '#1A1A1A']}
      style={styles.container}
    >
      <SafeAreaView style={styles.safeArea}>
        <KeyboardAvoidingView 
          style={styles.keyboardView}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
          {renderCurrentStep()}
        </KeyboardAvoidingView>
      </SafeAreaView>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  keyboardView: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: 24,
  },
  stepContainer: {
    flex: 1,
    justifyContent: 'center',
    maxWidth: 400,
    alignSelf: 'center',
    width: '100%',
  },
  title: {
    fontSize: 24,
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 8,
  },
  brandTitle: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 12,
  },
  stepTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 12,
  },
  subtitle: {
    fontSize: 16,
    color: '#AAAAAA',
    textAlign: 'center',
    marginBottom: 40,
  },
  stepSubtitle: {
    fontSize: 16,
    color: '#AAAAAA',
    textAlign: 'center',
    marginBottom: 32,
  },
  featureHighlights: {
    marginBottom: 40,
    alignItems: 'center',
  },
  featureText: {
    fontSize: 16,
    color: '#FFFFFF',
    marginBottom: 8,
  },
  authToggle: {
    flexDirection: 'row',
    backgroundColor: '#2A2A2A',
    borderRadius: 25,
    marginBottom: 24,
    padding: 4,
  },
  toggleButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 21,
  },
  activeToggle: {
    backgroundColor: '#7C4DFF',
  },
  toggleText: {
    color: '#AAAAAA',
    fontSize: 16,
    fontWeight: '600',
  },
  activeToggleText: {
    color: '#FFFFFF',
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    color: '#FFFFFF',
    marginBottom: 8,
    fontWeight: '600',
  },
  input: {
    backgroundColor: '#2A2A2A',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: '#FFFFFF',
    marginBottom: 16,
  },
  emojiGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  emojiOption: {
    width: 50,
    height: 50,
    backgroundColor: '#2A2A2A',
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  selectedEmoji: {
    backgroundColor: '#7C4DFF',
  },
  emojiText: {
    fontSize: 24,
  },
  primaryButton: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  gradientButton: {
    paddingVertical: 16,
    paddingHorizontal: 24,
    alignItems: 'center',
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  secondaryButton: {
    backgroundColor: '#2A2A2A',
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 24,
    alignItems: 'center',
  },
  secondaryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  buttonGroup: {
    flexDirection: 'row',
    marginTop: 24,
  },
  optionButton: {
    backgroundColor: '#2A2A2A',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    alignItems: 'center',
  },
  optionEmoji: {
    fontSize: 32,
    marginBottom: 8,
  },
  optionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  optionDescription: {
    fontSize: 14,
    color: '#AAAAAA',
    textAlign: 'center',
  },
});

export default OnboardingScreen; 