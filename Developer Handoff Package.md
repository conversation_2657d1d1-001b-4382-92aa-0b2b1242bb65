# 🗂 **Secret Circle - Developer Handoff Package**

## Project Name

**Secret Circle** – Private Social App for Close Friend Groups

## Tech Stack

| **Technology**   | **Tool**                                                       |
| ---------------- | -------------------------------------------------------------- |
| Framework        | **Expo (React Native)**                                        |
| Backend          | **Supabase (Auth, Realtime, Storage, Database)**               |
| Media Storage    | **Supabase Storage** or **AWS S3 (optional fallback)**         |
| State Management | **Zustand** or **React Context**                               |
| Navigation       | **React Navigation**                                           |
| Animations       | **React Native Reanimated** + **React Native Gesture Handler** |
| Notifications    | **Expo Push Notifications**                                    |

---

# 🗃 **Folder Structure**

```
/SecretCircle
│
├── /assets
│   ├── /icons
│   ├── /images
│   └── /fonts
│
├── /components
│   ├── CircleCard.jsx
│   ├── PostCard.jsx
│   ├── ReactionBar.jsx
│   ├── MoodPingButton.jsx
│   ├── MemoryVaultItem.jsx
│
├── /screens
│   ├── OnboardingScreen.jsx
│   ├── CircleHomeScreen.jsx
│   ├── PostCreationScreen.jsx
│   ├── MemoryVaultScreen.jsx
│   ├── SettingsScreen.jsx
│
├── /services
│   ├── supabaseClient.js
│   ├── mediaUpload.js
│   └── notificationService.js
│
├── /store
│   └── circleStore.js (Zustand)
│
├── /utils
│   ├── timeUtils.js
│   └── permissions.js
│
├── App.js
├── app.json (Expo config)
└── README.md
```

---

# 🎨 **Branding & UI**

## Colors

| **Name**         | **Hex**           |
| ---------------- | ----------------- |
| Primary Gradient | #7C4DFF → #18FFFF |
| Background       | #0D0D0D           |
| Accent           | #FF4081           |
| Text             | #FFFFFF           |
| Subtext          | #AAAAAA           |

## Fonts

* Primary: **Inter SemiBold**
* Secondary: **Inter Regular**

## Icon Pack

* **Phosphor Icons** or **Lucide React Native**

---

# 📲 **Screens & Features**

## 1. **Onboarding**

* Sign up / Login (Phone or Email)
* Create or Join Circle
* Choose Circle Emoji / Theme

## 2. **Circle Home**

* Show current Circle mood
* Display feed of disappearing posts
* Swipe to other Circles

## 3. **Post Creation**

* Camera / Video (15s max)
* Text
* Voice Note (10s max)

## 4. **Reactions**

* Emoji picker
* Voice reply (5s)
* Video reply (5s)
* Reacting extends post life by 24h

## 5. **Memory Vault**

* Majority vote to save posts
* Organize by folders (optional in V2)

## 6. **Mood Ping**

* Set mood with emoji or color
* Group mood resets daily

## 7. **Settings**

* Push notifications toggle
* Privacy preferences
* Leave Circle

---

# 🔗 **APIs & Backend Requirements**

### Auth

| Action          | Endpoint      |
| --------------- | ------------- |
| Sign Up / Login | Supabase Auth |

### Circles

| Action        | Method                      |
| ------------- | --------------------------- |
| Create Circle | POST `/circles`             |
| Invite Friend | POST `/circles/invite`      |
| Join Circle   | POST `/circles/join`        |
| Leave Circle  | DELETE `/circles/:id/leave` |

### Content

| Action          | Method                  |
| --------------- | ----------------------- |
| Upload Post     | POST `/posts`           |
| Get Circle Feed | GET `/posts?circle_id=` |
| React to Post   | POST `/reactions`       |
| Save to Vault   | POST `/vault`           |

### Moods

| Action   | Method                 |
| -------- | ---------------------- |
| Set Mood | POST `/mood`           |
| Get Mood | GET `/mood?circle_id=` |

---

# 🔐 **Privacy & Security**

* **End-to-end encrypted content storage**
* **No public feeds**
* **No screenshots (try to use Screen Capture detection via `react-native-detector`, optional fallback)**
* **Content autodeletes after 24 hours (unless reacted to)**
* **Strict invite-only system**

---

# 🚀 **MVP vs Phase 2**

| Feature                         | MVP | Phase 2 |
| ------------------------------- | --- | ------- |
| Create / Join Circle            | ✅   |         |
| Disappearing Posts              | ✅   |         |
| Reactions (Emoji, Voice, Video) | ✅   |         |
| Memory Vault (Basic)            | ✅   |         |
| Mood Ping                       | ✅   |         |
| AR Filters                      |     | ✅       |
| Custom Avatars                  |     | ✅       |
| Mini Games                      |     | ✅       |

---

# 🧪 **Testing**

* Use **Expo Go** for live testing
* Optional **Detox for E2E tests**
* Test devices: iPhone, Android (Pixel)
* Manual QA script provided in `/docs/qa-checklist.md` (to be filled)

---

# 📥 **Assets Included**

* [ ] App Icon (PNG + SVG)
* [ ] Splash Screen (PNG)
* [ ] Brand Fonts (.ttf or Google Fonts link)
* [ ] Mood Emojis (set of 10, PNG)
* [ ] Reaction Sounds (optional)

---

# 📝 **README Quickstart (Drop in Cursor)**

````markdown
# Secret Circle – Setup Guide

## Install Dependencies

```bash
npm install
````

## Run App (Expo)

```bash
npx expo start
```

## Environment Variables

Create a `.env` file:

```
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_anon_key
```

## Scripts

* `npm run start` – Run development server
* `npm run lint` – Run linter
* `npm run build` – Build project

## Notes

* Focus on MVP core features first
* Stick to the provided folder structure for scalability
* Use Expo Go for initial testing, TestFlight/Play Store builds for beta

```

---