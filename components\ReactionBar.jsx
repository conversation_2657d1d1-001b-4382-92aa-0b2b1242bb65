import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView, Alert } from 'react-native';
import { Audio } from 'expo-av';

const ReactionBar = ({ post, onReact, onSaveToVault }) => {
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [isRecordingVoice, setIsRecordingVoice] = useState(false);
  const [recording, setRecording] = useState(null);

  const reactions = post.reactions || [];
  const reactionCounts = reactions.reduce((acc, reaction) => {
    if (reaction.type === 'emoji') {
      acc[reaction.content] = (acc[reaction.content] || 0) + 1;
    }
    return acc;
  }, {});

  const commonEmojis = ['❤️', '😂', '😮', '😢', '😡', '👍', '👎', '🔥', '💯', '✨'];

  const handleEmojiReact = async (emoji) => {
    try {
      await onReact(post.id, 'emoji', emoji);
      setShowEmojiPicker(false);
    } catch (error) {
      Alert.alert('Error', 'Failed to add reaction');
    }
  };

  const startVoiceRecording = async () => {
    try {
      const { status } = await Audio.requestPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Microphone permission is required for voice reactions');
        return;
      }

      setIsRecordingVoice(true);
      
      const recordingOptions = {
        android: {
          extension: '.m4a',
          outputFormat: Audio.RECORDING_OPTION_ANDROID_OUTPUT_FORMAT_MPEG_4,
          audioEncoder: Audio.RECORDING_OPTION_ANDROID_AUDIO_ENCODER_AAC,
          sampleRate: 44100,
          numberOfChannels: 2,
          bitRate: 128000,
        },
        ios: {
          extension: '.m4a',
          outputFormat: Audio.RECORDING_OPTION_IOS_OUTPUT_FORMAT_MPEG4AAC,
          audioQuality: Audio.RECORDING_OPTION_IOS_AUDIO_QUALITY_HIGH,
          sampleRate: 44100,
          numberOfChannels: 2,
          bitRate: 128000,
          linearPCMBitDepth: 16,
          linearPCMIsBigEndian: false,
          linearPCMIsFloat: false,
        },
      };

      const { recording: newRecording } = await Audio.Recording.createAsync(recordingOptions);
      setRecording(newRecording);
    } catch (error) {
      console.error('Failed to start recording', error);
      setIsRecordingVoice(false);
      Alert.alert('Error', 'Failed to start voice recording');
    }
  };

  const stopVoiceRecording = async () => {
    try {
      if (!recording) return;

      setIsRecordingVoice(false);
      await recording.stopAndUnloadAsync();
      const uri = recording.getURI();
      
      // Upload and react with voice
      await onReact(post.id, 'voice', uri);
      setRecording(null);
    } catch (error) {
      console.error('Failed to stop recording', error);
      Alert.alert('Error', 'Failed to save voice reaction');
    }
  };

  const handleSaveToVault = async () => {
    try {
      await onSaveToVault(post.id);
    } catch (error) {
      Alert.alert('Error', 'Failed to save to Memory Vault');
    }
  };

  return (
    <View style={styles.container}>
      {/* Display existing reactions */}
      {Object.keys(reactionCounts).length > 0 && (
        <ScrollView horizontal style={styles.reactionsDisplay} showsHorizontalScrollIndicator={false}>
          {Object.entries(reactionCounts).map(([emoji, count]) => (
            <View key={emoji} style={styles.reactionBubble}>
              <Text style={styles.reactionEmoji}>{emoji}</Text>
              <Text style={styles.reactionCount}>{count}</Text>
            </View>
          ))}
        </ScrollView>
      )}

      {/* Reaction buttons */}
      <View style={styles.actionBar}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => setShowEmojiPicker(!showEmojiPicker)}
        >
          <Text style={styles.actionIcon}>😊</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, isRecordingVoice && styles.recordingButton]}
          onPressIn={startVoiceRecording}
          onPressOut={stopVoiceRecording}
        >
          <Text style={styles.actionIcon}>🎤</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.actionButton}
          onPress={handleSaveToVault}
        >
          <Text style={styles.actionIcon}>💾</Text>
        </TouchableOpacity>
      </View>

      {/* Emoji picker */}
      {showEmojiPicker && (
        <View style={styles.emojiPicker}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {commonEmojis.map((emoji) => (
              <TouchableOpacity
                key={emoji}
                style={styles.emojiButton}
                onPress={() => handleEmojiReact(emoji)}
              >
                <Text style={styles.emojiText}>{emoji}</Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      )}
      
      {/* Recording indicator */}
      {isRecordingVoice && (
        <View style={styles.recordingIndicator}>
          <View style={styles.recordingDot} />
          <Text style={styles.recordingText}>Recording... Release to send</Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  reactionsDisplay: {
    marginBottom: 12,
  },
  reactionBubble: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(124, 77, 255, 0.2)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 8,
  },
  reactionEmoji: {
    fontSize: 14,
    marginRight: 4,
  },
  reactionCount: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  actionBar: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  actionButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  recordingButton: {
    backgroundColor: 'rgba(255, 64, 129, 0.3)',
    transform: [{ scale: 1.1 }],
  },
  actionIcon: {
    fontSize: 20,
  },
  emojiPicker: {
    marginTop: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 12,
    padding: 8,
  },
  emojiButton: {
    padding: 8,
    marginHorizontal: 4,
  },
  emojiText: {
    fontSize: 24,
  },
  recordingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 8,
    padding: 8,
    backgroundColor: 'rgba(255, 64, 129, 0.1)',
    borderRadius: 8,
  },
  recordingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#FF4081',
    marginRight: 8,
  },
  recordingText: {
    color: '#FF4081',
    fontSize: 12,
    fontWeight: '600',
  },
});

export default ReactionBar; 