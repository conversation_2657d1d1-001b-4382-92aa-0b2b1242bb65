import { supabase } from './supabaseClient';
import * as ImagePicker from 'expo-image-picker';
import { Audio } from 'expo-av';

export const mediaUpload = {
  // Upload image to Supabase storage
  uploadImage: async (uri, fileName, bucket = 'circle-content') => {
    try {
      const formData = new FormData();
      formData.append('file', {
        uri,
        type: 'image/jpeg',
        name: fileName,
      });

      const { data, error } = await supabase.storage
        .from(bucket)
        .upload(fileName, formData, {
          contentType: 'image/jpeg',
        });

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      return { data: null, error };
    }
  },

  // Upload video to Supabase storage
  uploadVideo: async (uri, fileName, bucket = 'circle-content') => {
    try {
      const formData = new FormData();
      formData.append('file', {
        uri,
        type: 'video/mp4',
        name: fileName,
      });

      const { data, error } = await supabase.storage
        .from(bucket)
        .upload(fileName, formData, {
          contentType: 'video/mp4',
        });

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      return { data: null, error };
    }
  },

  // Upload audio to Supabase storage
  uploadAudio: async (uri, fileName, bucket = 'circle-content') => {
    try {
      const formData = new FormData();
      formData.append('file', {
        uri,
        type: 'audio/m4a',
        name: fileName,
      });

      const { data, error } = await supabase.storage
        .from(bucket)
        .upload(fileName, formData, {
          contentType: 'audio/m4a',
        });

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      return { data: null, error };
    }
  },

  // Get public URL for uploaded media
  getPublicUrl: (bucket, path) => {
    const { data } = supabase.storage.from(bucket).getPublicUrl(path);
    return data.publicUrl;
  },

  // Delete media from storage
  deleteMedia: async (bucket, path) => {
    try {
      const { data, error } = await supabase.storage
        .from(bucket)
        .remove([path]);

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      return { data: null, error };
    }
  },

  // Pick image from device
  pickImage: async () => {
    const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
    
    if (permissionResult.granted === false) {
      throw new Error('Permission to access camera roll is required');
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });

    return result;
  },

  // Take photo with camera
  takePhoto: async () => {
    const permissionResult = await ImagePicker.requestCameraPermissionsAsync();
    
    if (permissionResult.granted === false) {
      throw new Error('Permission to access camera is required');
    }

    const result = await ImagePicker.launchCameraAsync({
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });

    return result;
  },

  // Record video (up to 15 seconds as per PRD)
  recordVideo: async () => {
    const permissionResult = await ImagePicker.requestCameraPermissionsAsync();
    
    if (permissionResult.granted === false) {
      throw new Error('Permission to access camera is required');
    }

    const result = await ImagePicker.launchCameraAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Videos,
      allowsEditing: true,
      videoMaxDuration: 15, // 15 seconds max as per PRD
      quality: ImagePicker.UIImagePickerControllerQualityType.Medium,
    });

    return result;
  },
}; 