import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Switch,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useCircleStore } from '../store/circleStore';

const SettingsScreen = ({ navigation }) => {
  const [notifications, setNotifications] = useState({
    newPosts: true,
    reactions: true,
    memoryVault: true,
    moodPings: false,
  });

  const [privacy, setPrivacy] = useState({
    screenshotAlerts: true,
    onlineStatus: false,
    readReceipts: true,
  });

  const { user, clearUser, activeCircle } = useCircleStore();

  const handleLogout = () => {
    Alert.alert(
      'Log Out',
      'Are you sure you want to log out of Secret Circle?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Log Out',
          style: 'destructive',
          onPress: () => {
            clearUser();
            // Navigation will automatically reset to Onboarding when user is cleared
          },
        },
      ]
    );
  };

  const handleLeaveCircle = () => {
    if (!activeCircle) return;

    Alert.alert(
      'Leave Circle',
      `Are you sure you want to leave "${activeCircle.name}"? You'll need a new invite to rejoin.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Leave',
          style: 'destructive',
          onPress: () => {
            // TODO: Implement leave circle functionality
            Alert.alert('Left Circle', 'You have left the circle');
          },
        },
      ]
    );
  };

  const handleDeleteAccount = () => {
    Alert.alert(
      'Delete Account',
      'This will permanently delete your account and all your data. This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete Account',
          style: 'destructive',
          onPress: () => {
            Alert.alert(
              'Are you absolutely sure?',
              'Type "DELETE" to confirm account deletion',
              [
                { text: 'Cancel', style: 'cancel' },
                {
                  text: 'DELETE',
                  style: 'destructive',
                  onPress: () => {
                    // TODO: Implement account deletion
                    Alert.alert('Account Deleted', 'Your account has been deleted');
                  },
                },
              ]
            );
          },
        },
      ]
    );
  };

  const renderSection = (title, children) => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>{title}</Text>
      <View style={styles.sectionContent}>
        {children}
      </View>
    </View>
  );

  const renderSettingItem = (title, subtitle, value, onValueChange, type = 'switch') => (
    <View style={styles.settingItem}>
      <View style={styles.settingInfo}>
        <Text style={styles.settingTitle}>{title}</Text>
        {subtitle && <Text style={styles.settingSubtitle}>{subtitle}</Text>}
      </View>
      {type === 'switch' && (
        <Switch
          value={value}
          onValueChange={onValueChange}
          trackColor={{ false: '#333333', true: '#7C4DFF' }}
          thumbColor={value ? '#FFFFFF' : '#AAAAAA'}
        />
      )}
    </View>
  );

  const renderActionItem = (title, subtitle, onPress, destructive = false) => (
    <TouchableOpacity style={styles.actionItem} onPress={onPress}>
      <View style={styles.settingInfo}>
        <Text style={[styles.settingTitle, destructive && styles.destructiveText]}>
          {title}
        </Text>
        {subtitle && <Text style={styles.settingSubtitle}>{subtitle}</Text>}
      </View>
      <Text style={[styles.actionArrow, destructive && styles.destructiveText]}>
        →
      </Text>
    </TouchableOpacity>
  );

  return (
    <LinearGradient
      colors={['#0D0D0D', '#1A1A1A']}
      style={styles.container}
    >
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Settings</Text>
          {user && (
            <Text style={styles.userInfo}>
              {user.username || user.email}
            </Text>
          )}
        </View>

        <ScrollView style={styles.scrollView}>
          {renderSection(
            'Notifications',
            <>
              {renderSettingItem(
                'New Posts',
                'Get notified when someone posts in your circles',
                notifications.newPosts,
                (value) => setNotifications(prev => ({ ...prev, newPosts: value }))
              )}
              {renderSettingItem(
                'Reactions',
                'Get notified when someone reacts to your posts',
                notifications.reactions,
                (value) => setNotifications(prev => ({ ...prev, reactions: value }))
              )}
              {renderSettingItem(
                'Memory Vault',
                'Get notified when posts are saved to the vault',
                notifications.memoryVault,
                (value) => setNotifications(prev => ({ ...prev, memoryVault: value }))
              )}
              {renderSettingItem(
                'Mood Pings',
                'Get notified when friends update their mood',
                notifications.moodPings,
                (value) => setNotifications(prev => ({ ...prev, moodPings: value }))
              )}
            </>
          )}

          {renderSection(
            'Privacy & Safety',
            <>
              {renderSettingItem(
                'Screenshot Alerts',
                'Alert circle when someone takes a screenshot',
                privacy.screenshotAlerts,
                (value) => setPrivacy(prev => ({ ...prev, screenshotAlerts: value }))
              )}
              {renderSettingItem(
                'Online Status',
                'Show when you\'re active in the app',
                privacy.onlineStatus,
                (value) => setPrivacy(prev => ({ ...prev, onlineStatus: value }))
              )}
              {renderSettingItem(
                'Read Receipts',
                'Show when you\'ve seen posts and reactions',
                privacy.readReceipts,
                (value) => setPrivacy(prev => ({ ...prev, readReceipts: value }))
              )}
            </>
          )}

          {renderSection(
            'About',
            <>
              {renderActionItem(
                'Help & Support',
                'Get help or report issues',
                () => Alert.alert('Help', 'Contact <NAME_EMAIL>')
              )}
              {renderActionItem(
                'Privacy Policy',
                'Learn how we protect your data',
                () => Alert.alert('Privacy Policy', 'View privacy policy online')
              )}
              {renderActionItem(
                'Terms of Service',
                'Read our terms and conditions',
                () => Alert.alert('Terms', 'View terms of service online')
              )}
              {renderActionItem(
                'App Version',
                'Version 1.0.0',
                () => Alert.alert('Version', 'You\'re running the latest version')
              )}
            </>
          )}

          {activeCircle && renderSection(
            'Circle Management',
            <>
              {renderActionItem(
                'Invite Friends',
                `Share ${activeCircle.name} with friends`,
                () => {
                  const inviteCode = `${activeCircle.name.substring(0, 3).toUpperCase()}${Math.random().toString(36).substring(2, 8).toUpperCase()}`;
                  Alert.alert(
                    'Invite Friends',
                    `Share this code: ${inviteCode}`,
                    [
                      { text: 'Copy Code', onPress: () => Alert.alert('Copied!') },
                      { text: 'OK' }
                    ]
                  );
                }
              )}
              {renderActionItem(
                'Circle Settings',
                'Manage circle preferences',
                () => Alert.alert('Circle Settings', 'Circle management features coming soon')
              )}
              {renderActionItem(
                'Leave Circle',
                'Leave this circle permanently',
                handleLeaveCircle,
                true
              )}
            </>
          )}

          {renderSection(
            'Account',
            <>
              {renderActionItem(
                'Log Out',
                'Sign out of your account',
                handleLogout,
                true
              )}
              {renderActionItem(
                'Delete Account',
                'Permanently delete your account and data',
                handleDeleteAccount,
                true
              )}
            </>
          )}

          <View style={styles.footer}>
            <Text style={styles.footerText}>
              Secret Circle - Private social for close friends
            </Text>
            <Text style={styles.footerSubtext}>
              Made with ❤️ for authentic connections
            </Text>
          </View>
        </ScrollView>
      </SafeAreaView>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: 24,
    borderBottomWidth: 1,
    borderBottomColor: '#333333',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  userInfo: {
    fontSize: 16,
    color: '#7C4DFF',
    fontWeight: '600',
  },
  scrollView: {
    flex: 1,
  },
  section: {
    marginTop: 32,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 16,
    paddingHorizontal: 20,
  },
  sectionContent: {
    backgroundColor: '#2A2A2A',
    marginHorizontal: 20,
    borderRadius: 16,
    overflow: 'hidden',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#333333',
  },
  actionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#333333',
  },
  settingInfo: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 2,
  },
  settingSubtitle: {
    fontSize: 14,
    color: '#AAAAAA',
    lineHeight: 18,
  },
  actionArrow: {
    fontSize: 18,
    color: '#AAAAAA',
    marginLeft: 12,
  },
  destructiveText: {
    color: '#FF4444',
  },
  footer: {
    alignItems: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  footerText: {
    fontSize: 16,
    color: '#AAAAAA',
    textAlign: 'center',
    marginBottom: 8,
  },
  footerSubtext: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
  },
});

export default SettingsScreen; 