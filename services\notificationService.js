import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import { Platform } from 'react-native';

// Configure notification behavior
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
  }),
});

export const notificationService = {
  // Register for push notifications
  registerForPushNotifications: async () => {
    let token;

    if (Platform.OS === 'android') {
      await Notifications.setNotificationChannelAsync('default', {
        name: 'default',
        importance: Notifications.AndroidImportance.MAX,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#FF231F7C',
      });
    }

    if (Device.isDevice) {
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;
      
      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }
      
      if (finalStatus !== 'granted') {
        throw new Error('Failed to get push token for push notification!');
      }
      
      token = (await Notifications.getExpoPushTokenAsync()).data;
    } else {
      throw new Error('Must use physical device for Push Notifications');
    }

    return token;
  },

  // Send local notification (for testing)
  sendLocalNotification: async (title, body, data = {}) => {
    await Notifications.scheduleNotificationAsync({
      content: {
        title,
        body,
        data,
      },
      trigger: { seconds: 1 },
    });
  },

  // Listen for notifications
  addNotificationListener: (callback) => {
    return Notifications.addNotificationReceivedListener(callback);
  },

  // Listen for notification responses (when user taps notification)
  addNotificationResponseListener: (callback) => {
    return Notifications.addNotificationResponseReceivedListener(callback);
  },

  // Remove notification listeners
  removeNotificationListener: (subscription) => {
    if (subscription) {
      Notifications.removeNotificationSubscription(subscription);
    }
  },

  // Clear all notifications
  clearAllNotifications: async () => {
    await Notifications.dismissAllNotificationsAsync();
  },
}; 