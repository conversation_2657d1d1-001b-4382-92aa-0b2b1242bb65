import React from 'react';
import { View, Text, Image, TouchableOpacity, StyleSheet } from 'react-native';
import { Video } from 'expo-av';
import { LinearGradient } from 'expo-linear-gradient';
import { timeUtils } from '../utils/timeUtils';

const MemoryVaultItem = ({ memory, onPress }) => {
  const { post, saved_at, votes } = memory;

  const renderThumbnail = () => {
    switch (post.type) {
      case 'image':
        return (
          <Image 
            source={{ uri: post.content_url }} 
            style={styles.thumbnail}
            resizeMode="cover"
          />
        );
      
      case 'video':
        return (
          <Video
            source={{ uri: post.content_url }}
            style={styles.thumbnail}
            resizeMode="cover"
            shouldPlay={false}
            isLooping={false}
            useNativeControls={false}
          />
        );
      
      case 'audio':
        return (
          <LinearGradient
            colors={['#7C4DFF', '#18FFFF']}
            style={styles.thumbnail}
          >
            <Text style={styles.audioIcon}>🎵</Text>
          </LinearGradient>
        );
      
      case 'text':
      default:
        return (
          <View style={styles.textThumbnail}>
            <Text style={styles.textPreview} numberOfLines={3}>
              {post.content}
            </Text>
          </View>
        );
    }
  };

  return (
    <TouchableOpacity 
      style={styles.container}
      onPress={() => onPress(memory)}
    >
      <View style={styles.thumbnailContainer}>
        {renderThumbnail()}
        
        {/* Overlay with memory info */}
        <LinearGradient
          colors={['transparent', 'rgba(0, 0, 0, 0.8)']}
          style={styles.overlay}
        >
          <View style={styles.overlayContent}>
            <Text style={styles.author}>
              by {post.users?.username || 'Unknown'}
            </Text>
            <Text style={styles.savedDate}>
              Saved {timeUtils.getRelativeTime(saved_at)}
            </Text>
          </View>
        </LinearGradient>

        {/* Votes indicator */}
        <View style={styles.votesContainer}>
          <Text style={styles.votesText}>{votes || 0} votes</Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    margin: 8,
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: '#1A1A1A',
  },
  thumbnailContainer: {
    position: 'relative',
    width: '100%',
    height: 120,
  },
  thumbnail: {
    width: '100%',
    height: '100%',
    backgroundColor: '#2A2A2A',
  },
  textThumbnail: {
    width: '100%',
    height: '100%',
    backgroundColor: '#2A2A2A',
    padding: 12,
    justifyContent: 'center',
  },
  textPreview: {
    color: '#FFFFFF',
    fontSize: 14,
    lineHeight: 18,
  },
  audioIcon: {
    fontSize: 32,
    textAlign: 'center',
    alignSelf: 'center',
    marginTop: 40,
  },
  overlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 60,
    justifyContent: 'flex-end',
  },
  overlayContent: {
    padding: 8,
  },
  author: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
    marginBottom: 2,
  },
  savedDate: {
    color: '#AAAAAA',
    fontSize: 10,
  },
  votesContainer: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: 'rgba(124, 77, 255, 0.8)',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  votesText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '600',
  },
});

export default MemoryVaultItem; 