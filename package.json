{"name": "secretcircle", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "@supabase/supabase-js": "^2.52.0", "expo": "~53.0.17", "expo-av": "^15.1.7", "expo-camera": "^16.1.11", "expo-device": "^7.1.4", "expo-image-picker": "^16.1.4", "expo-linear-gradient": "^14.1.5", "expo-media-library": "^17.1.7", "expo-notifications": "^0.31.4", "expo-permissions": "^14.4.0", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "^2.27.1", "react-native-reanimated": "^3.18.0", "react-native-safe-area-context": "^5.5.2", "react-native-screens": "^4.13.0", "react-native-vector-icons": "^10.2.0", "react-native-web": "^0.20.0", "zustand": "^5.0.6"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}