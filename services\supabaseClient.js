import { createClient } from '@supabase/supabase-js';
import Constants from 'expo-constants';

// Get Supabase credentials from app config
const supabaseUrl = Constants.expoConfig?.extra?.supabaseUrl || process.env.SUPABASE_URL || 'https://mock.supabase.co';
const supabaseAnonKey = Constants.expoConfig?.extra?.supabaseAnonKey || process.env.SUPABASE_ANON_KEY || 'mock-anon-key';

// Check if we have real Supabase credentials
const hasRealCredentials = supabaseUrl !== 'https://mock.supabase.co' && supabaseAnonKey !== 'mock-anon-key';

let supabase;

if (hasRealCredentials) {
  console.log('🔗 Connecting to real Supabase instance:', supabaseUrl);
  supabase = createClient(supabaseUrl, supabaseAnonKey, {
    auth: {
      persistSession: true,
      detectSessionInUrl: false,
    },
    realtime: {
      params: {
        eventsPerSecond: 10,
      },
    },
  });
} else {
  // Enhanced mock query builder that chains properly
  const createMockQueryBuilder = () => {
    const mockBuilder = {
      select: (columns) => createMockQueryBuilder(),
      insert: (data) => createMockQueryBuilder(),
      update: (data) => createMockQueryBuilder(),
      delete: () => createMockQueryBuilder(),
      eq: (column, value) => createMockQueryBuilder(),
      neq: (column, value) => createMockQueryBuilder(),
      gt: (column, value) => createMockQueryBuilder(),
      gte: (column, value) => createMockQueryBuilder(),
      lt: (column, value) => createMockQueryBuilder(),
      lte: (column, value) => createMockQueryBuilder(),
      like: (column, value) => createMockQueryBuilder(),
      ilike: (column, value) => createMockQueryBuilder(),
      in: (column, values) => createMockQueryBuilder(),
      is: (column, value) => createMockQueryBuilder(),
      not: (column, value) => createMockQueryBuilder(),
      order: (column, options) => createMockQueryBuilder(),
      limit: (count) => createMockQueryBuilder(),
      offset: (count) => createMockQueryBuilder(),
      single: () => createMockQueryBuilder(),
    };

    // Make it thenable (promise-like)
    mockBuilder.then = (resolve, reject) => {
      const mockData = { data: [], error: null };
      return Promise.resolve(mockData).then(resolve, reject);
    };

    mockBuilder.catch = (reject) => {
      return Promise.resolve({ data: [], error: null }).catch(reject);
    };

    return mockBuilder;
  };

  // Mock Supabase client for development
  supabase = {
    auth: {
      signUp: async () => ({ data: { user: null }, error: null }),
      signInWithPassword: async () => ({ data: { user: null }, error: null }),
      signOut: async () => ({ error: null }),
      getUser: async () => ({ data: { user: null }, error: null }),
      onAuthStateChange: (callback) => {
        // Call callback with initial state
        setTimeout(() => callback('SIGNED_OUT', null), 0);
        return { 
          data: { 
            subscription: { 
              unsubscribe: () => {} 
            } 
          } 
        };
      },
    },
    from: (table) => {
      console.log(`Mock Supabase: Querying table '${table}'`);
      return createMockQueryBuilder();
    },
    storage: {
      from: (bucket) => ({
        upload: async () => ({ data: null, error: null }),
        download: async () => ({ data: null, error: null }),
        getPublicUrl: () => ({ data: { publicUrl: 'mock-url' } }),
      }),
    },
  };
  
  console.log('🔧 Using mock Supabase client for development. Configure real credentials in app.config.js');
}

export { supabase };

// Helper functions for common operations
export const auth = {
  signUp: async (email, password) => {
    if (!hasRealCredentials) {
      console.log('Mock auth: Sign up attempt for', email);
      return { data: { user: { id: 'mock-user-id', email } }, error: null };
    }
    
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
    });
    return { data, error };
  },

  signIn: async (email, password) => {
    if (!hasRealCredentials) {
      console.log('Mock auth: Sign in attempt for', email);
      return { data: { user: { id: 'mock-user-id', email } }, error: null };
    }
    
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    return { data, error };
  },

  signOut: async () => {
    if (!hasRealCredentials) {
      console.log('Mock auth: Sign out');
      return { error: null };
    }
    
    const { error } = await supabase.auth.signOut();
    return { error };
  },

  getCurrentUser: async () => {
    if (!hasRealCredentials) {
      return { data: { user: null }, error: null };
    }
    
    return await supabase.auth.getUser();
  },

  onAuthStateChange: (callback) => {
    if (!hasRealCredentials) {
      // Call callback with mock initial state
      setTimeout(() => callback('SIGNED_OUT', null), 0);
      return { data: { subscription: { unsubscribe: () => {} } } };
    }
    
    return supabase.auth.onAuthStateChange(callback);
  },
};

export default supabase; 